"""
运行批量测试程序的简化版本
"""

from TestSSQ_Main import SSQBatchTester

def run_test():
    print("=== SSQ批量测试程序 ===")
    
    try:
        # 1. 初始化测试器并读取数据
        print("步骤1: 读取SSQ历史数据...")
        tester = SSQBatchTester()
        tester.load_data()
        
        # 读取筛选参数
        print("步骤1.1: 读取筛选参数...")
        tester.load_parameters()
        
        # 2. 使用预设参数进行测试
        print("步骤2: 使用预设参数...")
        start_period = 23001
        small_periods = 6
        large_periods = 40
        nr = 20
        nb = 6
        answer_periods = 6
        
        print(f"测试参数:")
        print(f"起始期号: {start_period}")
        print(f"小数据库期数: {small_periods}")
        print(f"大数据库期数: {large_periods}")
        print(f"红球数量: {nr}")
        print(f"蓝球数量: {nb}")
        print(f"答案数据库期数: {answer_periods}")
        
        # 3. 运行少量期数的测试
        print("步骤3: 开始批量测试（测试前5期）...")
        
        current_period = start_period
        test_count = 0
        max_test = 5
        
        while current_period is not None and test_count < max_test:
            try:
                # 检查是否有足够的历史数据
                small_db = tester.get_database_by_period_range(current_period, small_periods, "before")
                large_db = tester.get_database_by_period_range(current_period, large_periods, "before")
                
                if len(small_db) < small_periods or len(large_db) < large_periods:
                    print(f"期号{current_period}: 历史数据不足，跳过")
                    current_period = tester.get_next_period(current_period)
                    continue
                
                # 检查是否有足够的答案数据
                answer_db = tester.get_database_by_period_range(current_period, answer_periods, "after")
                if len(answer_db) < answer_periods:
                    print(f"期号{current_period}: 答案数据不足，测试结束")
                    break
                
                print(f"\n测试期号: {current_period}")
                
                # 获取4组号码
                from number_selector import NumberSelector
                selector = NumberSelector(small_db, large_db, tester.ssqhistory_all, tester.parameters)
                
                group1_red, group1_blue = selector.get_group1_numbers()
                group2_red_sorted, group2_blue_sorted = tester.get_group2_sorted_numbers(large_db, nr, nb)
                group3_red_sorted, group3_blue_sorted = tester.get_group3_sorted_numbers(large_db, nr, nb)
                group4_red, group4_blue = selector.get_group4_numbers_with_params(nr, nb)
                
                print(f"第1组红球: {sorted(group1_red)}")
                print(f"第1组蓝球: {sorted(group1_blue) if group1_blue else '无'}")
                print(f"第2组红球: {group2_red_sorted}")
                print(f"第2组蓝球: {group2_blue_sorted}")
                print(f"第3组红球: {group3_red_sorted}")
                print(f"第3组蓝球: {group3_blue_sorted}")
                print(f"第4组红球: {sorted(group4_red)}")
                print(f"第4组蓝球: {sorted(group4_blue) if group4_blue else '无'}")
                
                # 验证第2组和第3组不同
                if group2_red_sorted == group3_red_sorted and group2_blue_sorted == group3_blue_sorted:
                    print("❌ 第2组和第3组相同！")
                else:
                    print("✅ 第2组和第3组不同")
                
                test_count += 1
                current_period = tester.get_next_period(current_period)
                
            except Exception as e:
                print(f"处理期号{current_period}时发生错误: {e}")
                current_period = tester.get_next_period(current_period)
                continue
        
        print(f"\n测试完成，共测试{test_count}期")
        print("✅ 排序功能验证成功！")
        
    except Exception as e:
        print(f"程序执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_test()
