"""
号码选择模块
负责初选和筛选SSQ号码
"""

import pandas as pd
from itertools import combinations
from typing import List, Tuple, Set
from ssq_analyzer import SSQAnalyzer
from markov_analyzer import MarkovAnalyzer


class NumberSelector:
    """SSQ号码选择器"""

    def __init__(self, small_db: pd.DataFrame, large_db: pd.DataFrame, original_db: pd.DataFrame, parameters: dict):
        """
        初始化号码选择器

        Args:
            small_db: 小数据库
            large_db: 大数据库
            original_db: 原始数据库（用于检查重复）
            parameters: 筛选参数字典
        """
        self.small_db = small_db
        self.large_db = large_db
        self.original_db = original_db
        self.parameters = parameters

        # 预处理原始数据库，创建历史组合的集合以加速查找
        self.historical_combinations = self._build_historical_combinations_set()

        # 初始化马尔科夫分析器
        self.markov_analyzer = MarkovAnalyzer(large_db)

    def _build_historical_combinations_set(self) -> Set[Tuple[int, int, int, int, int, int, int]]:
        """
        构建历史组合的集合，用于快速重复检查

        Returns:
            历史组合的集合
        """
        historical_combinations = set()

        for _, row in self.original_db.iterrows():
            # 获取该期的红球和蓝球，红球按从小到大排序
            red_balls = sorted([row['红球1'], row['红球2'], row['红球3'],
                              row['红球4'], row['红球5'], row['红球6']])
            blue_ball = row['蓝球']

            # 创建组合元组并添加到集合中
            combination = tuple(red_balls + [blue_ball])
            historical_combinations.add(combination)


        return historical_combinations
    
    def get_group1_numbers(self) -> Tuple[Set[int], Set[int]]:
        """
        获取第1组复式红蓝球号码（基于小数据库去重）
        
        Returns:
            (红球号码集合, 蓝球号码集合)
        """
        # 提取小数据库中所有红球号码并去重
        red_balls = set()
        red_columns = ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']
        for col in red_columns:
            red_balls.update(self.small_db[col].tolist())
        
        # 提取小数据库中所有蓝球号码并去重
        blue_balls = set(self.small_db['蓝球'].tolist())
        
        return red_balls, blue_balls
    
    def get_group2_numbers(self) -> Tuple[Set[int], Set[int]]:
        """
        获取第2组复式红蓝球号码（基于大数据库概率最高的号码）
        使用默认数量：24个红球，6个蓝球

        Returns:
            (红球号码集合, 蓝球号码集合)
        """
        return self.get_group2_numbers_with_params(24, 6)

    def get_group2_numbers_with_params(self, nr: int, nb: int) -> Tuple[Set[int], Set[int]]:
        """
        获取第2组复式红蓝球号码（基于大数据库概率最高的号码）

        Args:
            nr: 红球数量
            nb: 蓝球数量

        Returns:
            (红球号码集合, 蓝球号码集合)
        """
        # 统计红球出现次数
        red_counts = {}
        red_columns = ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']

        for col in red_columns:
            for ball in self.large_db[col]:
                red_counts[ball] = red_counts.get(ball, 0) + 1

        # 获取概率最高的nr个红球
        # 按概率从大到小排序，如果概率相等则按号码从小到大排序
        sorted_red_balls = sorted(red_counts.items(), key=lambda x: (-x[1], x[0]))
        top_nr_red_balls = set([ball for ball, count in sorted_red_balls[:nr]])

        # 统计蓝球出现次数
        blue_counts = self.large_db['蓝球'].value_counts().to_dict()

        # 获取概率最高的nb个蓝球
        # 按概率从大到小排序，如果概率相等则按号码从小到大排序
        sorted_blue_balls = sorted(blue_counts.items(), key=lambda x: (-x[1], x[0]))
        top_nb_blue_balls = set([ball for ball, count in sorted_blue_balls[:nb]])

        return top_nr_red_balls, top_nb_blue_balls

    def get_group2_numbers_sorted(self, nr: int, nb: int) -> Tuple[List[int], List[int]]:
        """
        获取第2组复式红蓝球号码（基于大数据库概率最高的号码）- 返回排序后的列表

        Args:
            nr: 红球数量
            nb: 蓝球数量

        Returns:
            (红球号码列表, 蓝球号码列表) - 按概率从大到小排序，概率相等时按号码从小到大排序
        """
        # 统计红球出现次数
        red_counts = {}
        red_columns = ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']

        for col in red_columns:
            for ball in self.large_db[col]:
                red_counts[ball] = red_counts.get(ball, 0) + 1

        # 获取概率最高的nr个红球
        # 按概率从大到小排序，如果概率相等则按号码从小到大排序
        sorted_red_balls = sorted(red_counts.items(), key=lambda x: (-x[1], x[0]))
        top_nr_red_balls = [ball for ball, count in sorted_red_balls[:nr]]

        # 统计蓝球出现次数
        blue_counts = self.large_db['蓝球'].value_counts().to_dict()

        # 获取概率最高的nb个蓝球
        # 按概率从大到小排序，如果概率相等则按号码从小到大排序
        sorted_blue_balls = sorted(blue_counts.items(), key=lambda x: (-x[1], x[0]))
        top_nb_blue_balls = [ball for ball, count in sorted_blue_balls[:nb]]

        return top_nr_red_balls, top_nb_blue_balls

    def get_group3_markov_numbers(self, nr: int, nb: int) -> Tuple[Set[int], Set[int]]:
        """
        获取第3组复式红蓝球号码（基于马尔科夫链的号码迁移概率）

        Args:
            nr: 红球数量
            nb: 蓝球数量

        Returns:
            (红球号码集合, 蓝球号码集合)
        """
        # 获取最新一期的红蓝球号码
        latest_row = self.large_db.iloc[-1]
        latest_red_balls = [latest_row[f'红球{i}'] for i in range(1, 7)]
        latest_blue_ball = latest_row['蓝球']

        # 计算转移概率矩阵
        red_transition_matrix = self.markov_analyzer.calculate_red_ball_transition_matrix()
        blue_transition_matrix = self.markov_analyzer.calculate_blue_ball_transition_matrix()

        # 计算红球历史出现概率（用于马尔科夫计算）
        red_prob_df, _ = self.calculate_large_db_probabilities()

        # 计算马尔科夫概率
        red_markov_df, blue_markov_df = self.markov_analyzer.calculate_markov_probabilities(
            latest_red_balls, latest_blue_ball, red_transition_matrix, blue_transition_matrix, red_prob_df
        )

        # 获取概率最高的号码
        top_red_balls, top_blue_balls = self.markov_analyzer.get_top_markov_numbers(
            red_markov_df, blue_markov_df, nr, nb
        )

        return top_red_balls, top_blue_balls

    def get_group3_markov_numbers_sorted(self, nr: int, nb: int) -> Tuple[List[int], List[int]]:
        """
        获取第3组复式红蓝球号码（基于马尔科夫链的号码迁移概率）- 返回排序后的列表

        Args:
            nr: 红球数量
            nb: 蓝球数量

        Returns:
            (红球号码列表, 蓝球号码列表) - 按概率从大到小排序，概率相等时按号码从小到大排序
        """
        # 获取最新一期的红蓝球号码
        latest_row = self.large_db.iloc[-1]
        latest_red_balls = [latest_row[f'红球{i}'] for i in range(1, 7)]
        latest_blue_ball = latest_row['蓝球']

        # 计算转移概率矩阵
        red_transition_matrix = self.markov_analyzer.calculate_red_ball_transition_matrix()
        blue_transition_matrix = self.markov_analyzer.calculate_blue_ball_transition_matrix()

        # 计算红球历史出现概率（用于马尔科夫计算）
        red_prob_df, _ = self.calculate_large_db_probabilities()

        # 计算马尔科夫概率
        red_markov_df, blue_markov_df = self.markov_analyzer.calculate_markov_probabilities(
            latest_red_balls, latest_blue_ball, red_transition_matrix, blue_transition_matrix, red_prob_df
        )

        # 获取概率最高的号码（排序后的列表）
        top_red_balls, top_blue_balls = self.markov_analyzer.get_top_markov_numbers_sorted(
            red_markov_df, blue_markov_df, nr, nb
        )

        return top_red_balls, top_blue_balls

    def get_group3_numbers(self) -> Tuple[Set[int], Set[int]]:
        """
        获取第3组复式红蓝球号码（第1组和第2组的交集）
        使用默认参数：24个红球，6个蓝球

        Returns:
            (红球号码集合, 蓝球号码集合)
        """
        return self.get_group3_numbers_with_params(24, 6)

    def get_group4_numbers_with_params(self, nr: int, nb: int) -> Tuple[Set[int], Set[int]]:
        """
        获取第4组复式红蓝球号码（第1、2、3组的交集）

        Args:
            nr: 红球数量
            nb: 蓝球数量

        Returns:
            (红球号码集合, 蓝球号码集合)
        """
        group1_red, group1_blue = self.get_group1_numbers()
        group2_red, group2_blue = self.get_group2_numbers_with_params(nr, nb)
        group3_red, group3_blue = self.get_group3_markov_numbers(nr, nb)

        # 求三组交集
        final_red_balls = group1_red & group2_red & group3_red
        final_blue_balls = group1_blue & group2_blue & group3_blue

        # 如果求交集后蓝球号码数量为0，则将第4组复式红蓝球号码中蓝球号码定义成0
        if len(final_blue_balls) == 0:
            final_blue_balls = {0}



        return final_red_balls, final_blue_balls

    def get_group3_numbers_with_params(self, nr: int, nb: int) -> Tuple[Set[int], Set[int]]:
        """
        获取第3组复式红蓝球号码（第1组和第2组的交集）
        保留此方法以保持向后兼容性

        Args:
            nr: 红球数量
            nb: 蓝球数量

        Returns:
            (红球号码集合, 蓝球号码集合)
        """
        group1_red, group1_blue = self.get_group1_numbers()
        group2_red, group2_blue = self.get_group2_numbers_with_params(nr, nb)

        # 求交集
        final_red_balls = group1_red & group2_red
        final_blue_balls = group1_blue & group2_blue



        return final_red_balls, final_blue_balls
    
    def filter_combinations(self, red_balls: Set[int], blue_balls: Set[int]) -> List[Tuple[List[int], int]]:
        """
        筛选符合条件的号码组合
        
        Args:
            red_balls: 红球号码集合
            blue_balls: 蓝球号码集合
            
        Returns:
            符合条件的组合列表
        """
        print("\n开始筛选号码组合...")

        # 转换为排序后的列表
        red_list = sorted(list(red_balls))
        blue_list = sorted(list(blue_balls))

        # 处理蓝球为0的特殊情况
        if 0 in blue_balls:

            blue_list = [0]

        # 生成所有6个红球的组合
        red_combinations = list(combinations(red_list, 6))
        total_combinations = len(red_combinations) * len(blue_list)

        print(f"总共需要检查 {total_combinations} 个组合...")
        
        filtered_combinations = []
        checked_count = 0
        
        for red_combo in red_combinations:
            for blue_ball in blue_list:
                checked_count += 1
                if checked_count % 10000 == 0:
                    print(f"已检查 {checked_count}/{total_combinations} 个组合...")
                
                red_combo_list = list(red_combo)
                
                # 检查筛选条件
                if self._check_filter_conditions(red_combo_list, blue_ball):
                    filtered_combinations.append((red_combo_list, blue_ball))
        
        print(f"筛选之后，还有 {len(filtered_combinations)} 组红蓝球号码")
        return filtered_combinations
    
    def _check_filter_conditions(self, red_balls: List[int], blue_ball: int) -> bool:
        """
        检查组合是否符合筛选条件

        Args:
            red_balls: 红球号码列表
            blue_ball: 蓝球号码

        Returns:
            是否符合条件
        """
        # 1. 每组排列组合的号码需与原始数据库中每组红蓝球号码不重复（7个红蓝球号码全相同的组数为0）
        if blue_ball != 0 and self._is_duplicate_combination(red_balls, blue_ball):
            return False

        # 2. 红球大球号码数量需介于NumRB_min与NumRB_max之间
        big_red_count = sum(1 for ball in red_balls if SSQAnalyzer.is_big_ball(ball, False))
        if not (self.parameters['NumRB_min'] <= big_red_count <= self.parameters['NumRB_max']):
            return False

        # 3. 红球奇球号码数量需介于NumRQ_min与NumRQ_max之间
        odd_red_count = sum(1 for ball in red_balls if SSQAnalyzer.is_odd_ball(ball))
        if not (self.parameters['NumRQ_min'] <= odd_red_count <= self.parameters['NumRQ_max']):
            return False

        # 4. 红球质球号码数量需介于NumRZ_min与NumRZ_max之间
        prime_red_count = sum(1 for ball in red_balls if SSQAnalyzer.is_prime_ball(ball))
        if not (self.parameters['NumRZ_min'] <= prime_red_count <= self.parameters['NumRZ_max']):
            return False

        # 5. 红球和值需介于SumR_min与SumR_max之间
        red_sum = sum(red_balls)
        if not (self.parameters['SumR_min'] <= red_sum <= self.parameters['SumR_max']):
            return False

        # 6. 红球AC值需介于NumRAC_min与NumRAC_max之间
        ac_value = SSQAnalyzer.calculate_ac_value(sorted(red_balls))
        if not (self.parameters['NumRAC_min'] <= ac_value <= self.parameters['NumRAC_max']):
            return False

        return True

    def _is_duplicate_combination(self, red_balls: List[int], blue_ball: int) -> bool:
        """
        检查组合是否与原始数据库中的某一期完全一致

        Args:
            red_balls: 红球号码列表
            blue_ball: 蓝球号码

        Returns:
            是否为重复组合
        """
        # 将红球排序并创建组合元组
        sorted_red_balls = sorted(red_balls)
        combination = tuple(sorted_red_balls + [blue_ball])

        # 使用集合快速查找
        return combination in self.historical_combinations
    
    def analyze_filtered_combinations(self, filtered_combinations: List[Tuple[List[int], int]]) -> pd.DataFrame:
        """
        分析筛选后的组合
        
        Args:
            filtered_combinations: 筛选后的组合列表
            
        Returns:
            包含分析结果的DataFrame
        """
        # print("开始分析筛选后的组合...")

        # 预先计算概率表
        red_prob_df, blue_prob_df = self.calculate_large_db_probabilities()
        red_markov_df, blue_markov_df = self.calculate_markov_probabilities()

        results = []
        for i, (red_balls, blue_ball) in enumerate(filtered_combinations):
            if (i + 1) % 1000 == 0:
                print(f"已分析 {i + 1}/{len(filtered_combinations)} 组合...")

            # 确保红球按从小到大排序
            sorted_red_balls = sorted(red_balls)

            # 计算遗漏值（基于小数据库）
            omissions = SSQAnalyzer.calculate_omission(self.small_db, sorted_red_balls, blue_ball)

            # 获取历史出现概率
            red_hist_probs = self.markov_analyzer.get_probability_for_numbers(
                sorted_red_balls, red_prob_df, '红球号码', '历史出现概率'
            )
            if blue_ball == 0:
                blue_hist_prob = 0.0  # 蓝球为0时，历史概率设为0
            else:
                blue_hist_prob = self.markov_analyzer.get_probability_for_numbers(
                    [blue_ball], blue_prob_df, '蓝球号码', '历史出现概率'
                )[0]

            # 获取马尔科夫概率
            red_markov_probs = self.markov_analyzer.get_probability_for_numbers(
                sorted_red_balls, red_markov_df, '红球号码', '号码迁移概率'
            )
            if blue_ball == 0:
                blue_markov_prob = 0.0  # 蓝球为0时，马尔科夫概率设为0
            else:
                blue_markov_prob = self.markov_analyzer.get_probability_for_numbers(
                    [blue_ball], blue_markov_df, '蓝球号码', '号码迁移概率'
                )[0]

            # 构建结果行
            result_row = {
                '红球1': sorted_red_balls[0],
                '红球2': sorted_red_balls[1],
                '红球3': sorted_red_balls[2],
                '红球4': sorted_red_balls[3],
                '红球5': sorted_red_balls[4],
                '红球6': sorted_red_balls[5],
                '蓝球': blue_ball,
                '红球1遗漏': omissions[0],
                '红球2遗漏': omissions[1],
                '红球3遗漏': omissions[2],
                '红球4遗漏': omissions[3],
                '红球5遗漏': omissions[4],
                '红球6遗漏': omissions[5],
                '蓝球遗漏': omissions[6],
                '红球1历史概率': red_hist_probs[0],
                '红球2历史概率': red_hist_probs[1],
                '红球3历史概率': red_hist_probs[2],
                '红球4历史概率': red_hist_probs[3],
                '红球5历史概率': red_hist_probs[4],
                '红球6历史概率': red_hist_probs[5],
                '蓝球历史概率': blue_hist_prob,
                '红球1迁移概率': red_markov_probs[0],
                '红球2迁移概率': red_markov_probs[1],
                '红球3迁移概率': red_markov_probs[2],
                '红球4迁移概率': red_markov_probs[3],
                '红球5迁移概率': red_markov_probs[4],
                '红球6迁移概率': red_markov_probs[5],
                '蓝球迁移概率': blue_markov_prob
            }

            results.append(result_row)
        
        print("分析完成，正在保存结果。")
        
        # 转换为DataFrame
        results_df = pd.DataFrame(results)
        
        return results_df
    
    def calculate_large_db_probabilities(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        基于大数据库计算历史出现概率
        
        Returns:
            (红球概率表, 蓝球概率表)
        """
        # 统计红球出现次数
        red_counts = {}
        red_columns = ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']
        
        for col in red_columns:
            for ball in self.large_db[col]:
                red_counts[ball] = red_counts.get(ball, 0) + 1
        
        # 计算红球概率
        total_red_appearances = sum(red_counts.values())
        red_probabilities = []
        
        for ball in sorted(red_counts.keys()):
            probability = red_counts[ball] / total_red_appearances
            red_probabilities.append({'红球号码': ball, '历史出现概率': probability})
        
        red_prob_df = pd.DataFrame(red_probabilities)
        
        # 统计蓝球出现次数
        blue_counts = self.large_db['蓝球'].value_counts().to_dict()
        
        # 计算蓝球概率
        total_blue_appearances = sum(blue_counts.values())
        blue_probabilities = []
        
        for ball in sorted(blue_counts.keys()):
            probability = blue_counts[ball] / total_blue_appearances
            blue_probabilities.append({'蓝球号码': ball, '历史出现概率': probability})
        
        blue_prob_df = pd.DataFrame(blue_probabilities)
        
        return red_prob_df, blue_prob_df

    def calculate_markov_probabilities(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        计算马尔科夫链号码迁移概率

        Returns:
            (红球马尔科夫概率表, 蓝球马尔科夫概率表)
        """
        # 获取最新一期的红蓝球号码
        latest_row = self.large_db.iloc[-1]
        latest_red_balls = [latest_row[f'红球{i}'] for i in range(1, 7)]
        latest_blue_ball = latest_row['蓝球']

        # 计算转移概率矩阵
        red_transition_matrix = self.markov_analyzer.calculate_red_ball_transition_matrix()
        blue_transition_matrix = self.markov_analyzer.calculate_blue_ball_transition_matrix()

        # 计算红球历史出现概率（用于马尔科夫计算）
        red_prob_df, _ = self.calculate_large_db_probabilities()

        # 计算马尔科夫概率
        red_markov_df, blue_markov_df = self.markov_analyzer.calculate_markov_probabilities(
            latest_red_balls, latest_blue_ball, red_transition_matrix, blue_transition_matrix, red_prob_df
        )

        return red_markov_df, blue_markov_df

    def calculate_transition_tables(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        计算历史跟随性概率表

        Returns:
            (红球历史跟随性概率表, 蓝球历史跟随性概率表)
        """
        red_transition_table = self.markov_analyzer.get_red_ball_transition_table()
        blue_transition_table = self.markov_analyzer.get_blue_ball_transition_table()

        return red_transition_table, blue_transition_table
