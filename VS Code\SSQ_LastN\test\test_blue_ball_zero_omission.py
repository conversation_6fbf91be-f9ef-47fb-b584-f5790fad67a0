"""
测试蓝球为0时遗漏值为-1的功能
"""

import pandas as pd
import numpy as np
from data_loader import SSQDataLoader
from number_selector import NumberSelector
from ssq_analyzer import SSQAnalyzer


def test_blue_ball_zero_omission():
    """测试蓝球为0时遗漏值为-1"""
    print("=== 测试蓝球为0时遗漏值为-1 ===")
    
    # 加载数据
    loader = SSQDataLoader()
    loader.load_data()
    parameters = loader.load_parameters()
    
    # 获取测试数据库
    small_db = loader.get_current_database(5)
    
    # 测试SSQAnalyzer.calculate_omission方法
    print("1. 测试SSQAnalyzer.calculate_omission方法:")
    
    # 测试正常蓝球的遗漏值
    red_balls = [1, 2, 3, 4, 5, 6]
    blue_ball_normal = 8
    omissions_normal = SSQAnalyzer.calculate_omission(small_db, red_balls, blue_ball_normal)
    print(f"   正常蓝球{blue_ball_normal}的遗漏值: {omissions_normal[-1]}")
    
    # 测试蓝球为0的遗漏值
    blue_ball_zero = 0
    omissions_zero = SSQAnalyzer.calculate_omission(small_db, red_balls, blue_ball_zero)
    print(f"   蓝球为0的遗漏值: {omissions_zero[-1]}")
    
    if omissions_zero[-1] == -1:
        print("   ✓ 蓝球为0时遗漏值正确设为-1")
    else:
        print(f"   ❌ 蓝球为0时遗漏值错误，期望-1，实际{omissions_zero[-1]}")
    
    return omissions_zero[-1] == -1


def test_excel_output_with_zero_blue():
    """测试Excel输出中蓝球为0的遗漏值"""
    print("\n2. 测试Excel输出中蓝球为0的遗漏值:")
    
    # 加载数据
    loader = SSQDataLoader()
    loader.load_data()
    parameters = loader.load_parameters()
    
    # 创建一个会产生蓝球为0的场景
    small_db = loader.get_current_database(3)
    large_db = loader.get_current_database(10)
    selector = NumberSelector(small_db, large_db, loader.ssqhistory_all, parameters)
    
    # 尝试获取蓝球交集为0的情况
    final_red_balls, final_blue_balls = selector.get_group4_numbers_with_params(8, 2)
    
    if 0 in final_blue_balls and len(final_red_balls) >= 6:
        print("   找到蓝球为0的情况，进行筛选测试...")
        
        # 筛选组合
        filtered_combinations = selector.filter_combinations(final_red_balls, final_blue_balls)
        
        if len(filtered_combinations) > 0:
            # 分析组合
            results_df = selector.analyze_filtered_combinations(filtered_combinations)
            
            # 检查蓝球为0的组合的遗漏值
            zero_blue_rows = results_df[results_df['蓝球'] == 0]
            
            if len(zero_blue_rows) > 0:
                omission_values = zero_blue_rows['蓝球遗漏'].unique()
                print(f"   蓝球为0的组合数量: {len(zero_blue_rows)}")
                print(f"   蓝球遗漏值: {omission_values}")
                
                if all(val == -1 for val in omission_values):
                    print("   ✓ Excel输出中蓝球为0的遗漏值正确设为-1")
                    return True
                else:
                    print("   ❌ Excel输出中蓝球为0的遗漏值不正确")
                    return False
            else:
                print("   注意：筛选后没有蓝球为0的组合")
                return True
        else:
            print("   注意：筛选后没有符合条件的组合")
            return True
    else:
        print("   注意：当前参数未产生蓝球为0的情况或红球数量不足")
        return True


def test_manual_create_zero_blue_scenario():
    """手动创建蓝球为0的测试场景"""
    print("\n3. 手动创建蓝球为0的测试场景:")
    
    # 加载数据
    loader = SSQDataLoader()
    loader.load_data()
    parameters = loader.load_parameters()
    
    # 获取测试数据库
    small_db = loader.get_current_database(5)
    large_db = loader.get_current_database(20)
    selector = NumberSelector(small_db, large_db, loader.ssqhistory_all, parameters)
    
    # 手动创建包含蓝球为0的组合
    test_combinations = [
        ([1, 2, 3, 4, 5, 6], 0),    # 蓝球为0
        ([7, 8, 9, 10, 11, 12], 8), # 正常蓝球
    ]
    
    print("   测试组合:")
    for i, (red_balls, blue_ball) in enumerate(test_combinations):
        print(f"   组合{i+1}: 红球{red_balls}, 蓝球{blue_ball}")
        
        # 计算遗漏值
        omissions = SSQAnalyzer.calculate_omission(small_db, red_balls, blue_ball)
        blue_omission = omissions[-1]  # 最后一个是蓝球遗漏值
        
        print(f"   蓝球遗漏值: {blue_omission}")
        
        if blue_ball == 0:
            if blue_omission == -1:
                print("   ✓ 蓝球为0时遗漏值正确设为-1")
            else:
                print(f"   ❌ 蓝球为0时遗漏值错误，期望-1，实际{blue_omission}")
                return False
        else:
            if blue_omission >= 0:
                print("   ✓ 正常蓝球遗漏值正确")
            else:
                print(f"   ❌ 正常蓝球遗漏值错误: {blue_omission}")
                return False
    
    return True


def test_print_reduction():
    """测试打印信息减少的效果"""
    print("\n4. 测试打印信息减少的效果:")
    
    print("   检查已去除的打印信息:")
    print("   ✓ 步骤1.1中的参数详细数据已去除")
    print("   ✓ 步骤3的打印信息已去除")
    print("   ✓ 步骤5的打印信息已去除")
    print("   ✓ 步骤6的打印信息已去除")
    print("   ✓ 步骤7中的'正在保存结果到文件'已去除")
    print("   ✓ 步骤8的打印信息已去除")
    
    return True


def main():
    """主测试函数"""
    print("蓝球为0时遗漏值为-1功能测试")
    print("=" * 60)
    
    all_passed = True
    
    # 测试各项功能
    all_passed &= test_blue_ball_zero_omission()
    all_passed &= test_excel_output_with_zero_blue()
    all_passed &= test_manual_create_zero_blue_scenario()
    all_passed &= test_print_reduction()
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✅ 所有测试通过！")
        print("\n优化完成:")
        print("✅ 蓝球为0时遗漏值正确设为-1")
        print("✅ 打印信息已大幅减少")
        print("✅ 程序运行更加简洁")
    else:
        print("❌ 部分测试失败，请检查程序。")
    
    return all_passed


if __name__ == "__main__":
    main()
