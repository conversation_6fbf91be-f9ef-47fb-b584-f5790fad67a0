"""
SSQ选号程序主模块
基于SSQ历史数据进行选号分析
"""

import sys
import traceback
from data_loader import SSQDataLoader
from result_saver import ResultSaver


def get_user_input() -> tuple[int, int, int, int]:
    """
    获取用户输入的参数

    Returns:
        (小数据库期数, 大数据库期数, 红球数量NR, 蓝球数量NB)
    """
    while True:
        try:
            # 获取小数据库期数
            small_input = input("\n请输入当前数据库（小）的期数（正整数）: ").strip()
            small_periods = int(small_input)

            if small_periods <= 0:
                print("错误：期数必须为正整数，请重新输入。")
                continue

            # 获取大数据库期数
            large_input = input("请输入当前数据库（大）的期数（正整数）: ").strip()
            large_periods = int(large_input)

            if large_periods <= 0:
                print("错误：期数必须为正整数，请重新输入。")
                continue

            if large_periods < small_periods:
                print("错误：大数据库期数应该大于等于小数据库期数，请重新输入。")
                continue

            # 获取红球和蓝球数量
            while True:
                try:
                    nr_nb_input = input("请输入想要选择的红球数量和蓝球数量（用逗号隔开，如：20,6）: ").strip()
                    nr_str, nb_str = nr_nb_input.split(',')
                    nr = int(nr_str.strip())
                    nb = int(nb_str.strip())

                    if nr <= 0 or nb <= 0:
                        print("错误：红球和蓝球数量必须为正整数，请重新输入。")
                        continue

                    if nr < 6:
                        print("错误：红球数量不能少于6个（需要组成6红球+1蓝球的组合），请重新输入。")
                        continue

                    if nb < 1:
                        print("错误：蓝球数量不能少于1个，请重新输入。")
                        continue

                    if nr > 33:
                        print("错误：红球数量不能超过33个（红球总数），请重新输入。")
                        continue

                    if nb > 16:
                        print("错误：蓝球数量不能超过16个（蓝球总数），请重新输入。")
                        continue

                    return small_periods, large_periods, nr, nb

                except ValueError:
                    print("错误：请输入正确的格式，如：20,6")
                    continue

        except ValueError:
            print("错误：请输入有效的正整数。")
        except KeyboardInterrupt:
            print("\n程序已取消。")
            sys.exit(0)


def main():
    """主函数"""
    print("=" * 60)
    print("SSQ选号程序")
    print("基于历史数据进行初选和筛选分析")
    print("=" * 60)

    try:
        # 1. 读取数据
        print("\n步骤1: 读取SSQ历史数据...")
        data_loader = SSQDataLoader()
        data_loader.load_data()

        # 读取筛选参数
        print("\n步骤1.1: 读取筛选参数...")
        parameters = data_loader.load_parameters()

        # 显示数据基本信息
        latest_period, latest_red, latest_blue = data_loader.get_latest_period_info()
        print(f"最新一期: {latest_period}期")
        print(f"最新号码: {' '.join([f'{x:02d}' for x in latest_red])} + {latest_blue:02d}")

        # 2. 用户交互 - 定义数据库范围和选号参数
        print("\n步骤2: 定义数据库范围和选号参数...")

        while True:  # 循环直到获得有效的交集
            small_periods, large_periods, nr, nb = get_user_input()

            # 获取小数据库和大数据库
            small_db = data_loader.get_current_database(small_periods)
            large_db = data_loader.get_current_database(large_periods)

            print(f"小数据库: 最新{small_periods}期，期号范围 {small_db['期号'].min()}-{large_db['期号'].max()}")
            print(f"大数据库: 最新{large_periods}期，期号范围 {large_db['期号'].min()}-{large_db['期号'].max()}")
            print(f"选号参数: 红球{nr}个，蓝球{nb}个")

            # 3. 初选号码
            from number_selector import NumberSelector
            # 获取原始数据库用于重复检查
            original_db = data_loader.ssqhistory_all
            if original_db is None:
                raise ValueError("原始数据库未正确加载")
            selector = NumberSelector(small_db, large_db, original_db, parameters)

            # 获取4组复式红蓝球号码
            group1_red, group1_blue = selector.get_group1_numbers()
            group2_red, group2_blue = selector.get_group2_numbers_with_params(nr, nb)
            group3_red, group3_blue = selector.get_group3_markov_numbers(nr, nb)
            final_red_balls, final_blue_balls = selector.get_group4_numbers_with_params(nr, nb)

            # 获取排序后的第2组和第3组号码用于显示
            group2_red_sorted, group2_blue_sorted = selector.get_group2_numbers_sorted(nr, nb)
            group3_red_sorted, group3_blue_sorted = selector.get_group3_markov_numbers_sorted(nr, nb)

            # 检查交集结果
            if len(final_red_balls) == 0:
                print(f"交集结果：红球{len(final_red_balls)}个，蓝球{len(final_blue_balls)}个")
                print("提示：红球交集为0，输入的NR偏少，需重新输入。")
                print("建议：增加红球数量NR或增加数据库期数。")
                continue  # 重新开始用户交互

            # 如果蓝球交集为0，程序会自动将蓝球定义为0，继续执行
            if 0 in final_blue_balls:
                print("注意：蓝球交集为0，已自动将蓝球号码定义为0，程序继续执行。")

            # 检查是否满足组合要求
            if len(final_red_balls) < 6:
                print(f"错误：初选后红球数量({len(final_red_balls)})少于6个，无法进行组合。")
                print("建议：增加红球数量NR或增加数据库期数。")
                continue  # 重新开始用户交互

            # 交集有效，跳出循环
            break

        # 4. 筛选号码
        print("\n步骤4: 筛选号码...")
        filtered_combinations = selector.filter_combinations(final_red_balls, final_blue_balls)

        if len(filtered_combinations) == 0:
            print("警告：没有符合筛选条件的组合。")
            print("建议调整筛选条件或增加数据库期数。")
            return

        # 5. 分析筛选后的组合
        results_df = selector.analyze_filtered_combinations(filtered_combinations)

        # 6. 计算大数据库的历史概率、跟随性概率和马尔科夫概率
        red_prob_df, blue_prob_df = selector.calculate_large_db_probabilities()
        red_transition_df, blue_transition_df = selector.calculate_transition_tables()
        red_markov_df, blue_markov_df = selector.calculate_markov_probabilities()

        # 7. 保存结果

        saver = ResultSaver()

        # 保存到Excel文件
        saver.save_results(
            results_df,
            red_prob_df,
            blue_prob_df,
            red_transition_df,
            blue_transition_df,
            red_markov_df,
            blue_markov_df,
            latest_period,
            small_periods,
            large_periods,
            nr,
            nb,
            group1_red,
            group1_blue,
            group2_red,
            group2_blue,
            group3_red,
            group3_blue,
            final_red_balls,
            final_blue_balls,
            group2_red_sorted,
            group2_blue_sorted,
            group3_red_sorted,
            group3_blue_sorted
        )

        # 8. 显示结果摘要
        saver.print_sample_results(results_df, min(10, len(results_df)))
        saver.print_probability_summary(red_prob_df, blue_prob_df)




    except FileNotFoundError as e:
        print(f"错误：找不到数据文件 - {e}")
        print("请确保 lottery_data_all.xlsx 文件存在于当前目录中。")
    except Exception as e:
        print(f"程序执行过程中发生错误: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()

    input("\n按回车键退出...")


if __name__ == "__main__":
    main()
