"""
排列组合生成模块
负责生成SSQ号码的排列组合
"""

import pandas as pd
from itertools import combinations
from typing import List, Tuple, Set
from ssq_analyzer import SSQAnalyzer


class CombinationGenerator:
    """SSQ号码排列组合生成器"""
    
    def __init__(self, current_db: pd.DataFrame):
        """
        初始化组合生成器
        
        Args:
            current_db: 当前数据库
        """
        self.current_db = current_db
        self.unique_red_balls = set()
        self.unique_blue_balls = set()
        self._extract_unique_balls()
    
    def _extract_unique_balls(self):
        """从当前数据库中提取去重后的红蓝球号码"""
        # 提取所有红球号码并去重
        red_columns = ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']
        for col in red_columns:
            self.unique_red_balls.update(self.current_db[col].tolist())
        
        # 提取所有蓝球号码并去重
        self.unique_blue_balls.update(self.current_db['蓝球'].tolist())
        
        # 转换为排序后的列表
        self.unique_red_balls = sorted(list(self.unique_red_balls))
        self.unique_blue_balls = sorted(list(self.unique_blue_balls))
    
    def get_unique_balls_info(self) -> Tuple[int, int, int]:
        """
        获取去重后的球号码信息
        
        Returns:
            (红球个数, 蓝球个数, 总组合数)
        """
        red_count = len(self.unique_red_balls)
        blue_count = len(self.unique_blue_balls)
        
        # 计算总组合数：C(红球个数, 6) * C(蓝球个数, 1)
        from math import comb
        total_combinations = comb(red_count, 6) * blue_count
        
        return red_count, blue_count, total_combinations
    
    def generate_all_combinations(self) -> List[Tuple[List[int], int]]:
        """
        生成所有可能的红蓝球组合
        
        Returns:
            所有组合的列表，每个元素为(红球列表, 蓝球)
        """
        all_combinations = []
        
        # 生成所有6个红球的组合
        red_combinations = list(combinations(self.unique_red_balls, 6))
        
        # 为每个红球组合配上每个蓝球
        for red_combo in red_combinations:
            for blue_ball in self.unique_blue_balls:
                all_combinations.append((list(red_combo), blue_ball))
        
        return all_combinations
    
    def analyze_all_combinations(self) -> pd.DataFrame:
        """
        分析所有组合的特性
        
        Returns:
            包含所有组合分析结果的DataFrame
        """
        print("开始生成排列组合...")
        
        # 获取基本信息
        red_count, blue_count, total_combinations = self.get_unique_balls_info()
        print(f"红球号码（去重复后）有 {red_count} 个")
        print(f"蓝球号码（去重复后）有 {blue_count} 个")
        print(f"总共排列组合了 {total_combinations} 组红蓝球号码")
        
        # 生成所有组合
        all_combinations = self.generate_all_combinations()
        
        # print("开始分析组合特性...")
        
        # 分析每个组合的特性
        results = []
        for i, (red_balls, blue_ball) in enumerate(all_combinations):
            if (i + 1) % 10000 == 0:
                print(f"已分析 {i + 1}/{total_combinations} 组合...")
            
            analysis = SSQAnalyzer.analyze_combination(red_balls, blue_ball, self.current_db)
            
            # 构建结果行
            result_row = {
                '红球1': analysis['red_balls'][0],
                '红球2': analysis['red_balls'][1],
                '红球3': analysis['red_balls'][2],
                '红球4': analysis['red_balls'][3],
                '红球5': analysis['red_balls'][4],
                '红球6': analysis['red_balls'][5],
                '蓝球': analysis['blue_ball'],
                '红球1遗漏': analysis['omissions'][0],
                '红球2遗漏': analysis['omissions'][1],
                '红球3遗漏': analysis['omissions'][2],
                '红球4遗漏': analysis['omissions'][3],
                '红球5遗漏': analysis['omissions'][4],
                '红球6遗漏': analysis['omissions'][5],
                '蓝球遗漏': analysis['omissions'][6],
                '红球大球个数': analysis['red_big_count'],
                '红球奇球个数': analysis['red_odd_count'],
                '红球质球个数': analysis['red_prime_count'],
                '红球和值': analysis['red_sum'],
                '红球AC值': analysis['ac_value']
            }
            
            results.append(result_row)
        
        print("分析完成，正在保存结果。")
        
        # 转换为DataFrame
        results_df = pd.DataFrame(results)
        
        return results_df
    
    def calculate_historical_probabilities(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        计算历史出现概率
        
        Returns:
            (红球概率表, 蓝球概率表)
        """
        # 统计红球出现次数
        red_counts = {}
        red_columns = ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']
        
        for col in red_columns:
            for ball in self.current_db[col]:
                red_counts[ball] = red_counts.get(ball, 0) + 1
        
        # 计算红球概率
        total_red_appearances = sum(red_counts.values())
        red_probabilities = []
        
        for ball in sorted(red_counts.keys()):
            probability = red_counts[ball] / total_red_appearances
            red_probabilities.append({'红球号码': ball, '历史出现概率': probability})
        
        red_prob_df = pd.DataFrame(red_probabilities)
        
        # 统计蓝球出现次数
        blue_counts = self.current_db['蓝球'].value_counts().to_dict()
        
        # 计算蓝球概率
        total_blue_appearances = sum(blue_counts.values())
        blue_probabilities = []
        
        for ball in sorted(blue_counts.keys()):
            probability = blue_counts[ball] / total_blue_appearances
            blue_probabilities.append({'蓝球号码': ball, '历史出现概率': probability})
        
        blue_prob_df = pd.DataFrame(blue_probabilities)
        
        return red_prob_df, blue_prob_df
