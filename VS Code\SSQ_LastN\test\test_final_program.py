"""
最终版SSQ选号程序测试脚本
验证所有新增功能
"""

import sys
import traceback
import pandas as pd
import numpy as np
from data_loader import SSQDataLoader
from number_selector import NumberSelector
from markov_analyzer import MarkovAnalyzer
from result_saver import ResultSaver


def test_data_loader_with_parameters():
    """测试数据加载器的参数读取功能"""
    print("=== 测试数据加载器（含参数读取） ===")
    try:
        loader = SSQDataLoader()
        data = loader.load_data()
        print(f"✓ 数据加载成功，共{len(data)}期")
        
        # 测试参数读取
        parameters = loader.load_parameters()
        print(f"✓ 参数读取成功，共{len(parameters)}个参数")
        
        # 验证参数完整性
        expected_params = ['NumRB_min', 'NumRB_max', 'NumRQ_min', 'NumRQ_max', 
                          'NumRZ_min', 'NumRZ_max', 'SumR_min', 'SumR_max', 
                          'NumRAC_min', 'NumRAC_max']
        
        for param in expected_params:
            if param in parameters:
                print(f"✓ 参数{param}: {parameters[param]}")
            else:
                print(f"✗ 缺少参数{param}")
                return False
        
        return True, loader, parameters
    except Exception as e:
        print(f"✗ 数据加载器测试失败: {e}")
        return False, None, None


def test_markov_analyzer(large_db):
    """测试马尔科夫分析器"""
    print("\n=== 测试马尔科夫分析器 ===")
    try:
        analyzer = MarkovAnalyzer(large_db)
        
        # 测试红球转移矩阵
        red_matrix = analyzer.calculate_red_ball_transition_matrix()
        print(f"✓ 红球转移矩阵计算成功，形状: {red_matrix.shape}")
        
        # 验证概率矩阵性质（每列的概率和应该为1）
        for j in range(33):
            col_sum = np.sum(red_matrix[:, j])
            if not np.isclose(col_sum, 1.0, atol=1e-6):
                print(f"✗ 红球转移矩阵第{j+1}列概率和不为1: {col_sum}")
                return False
        print("✓ 红球转移矩阵概率验证通过（每列概率和为1）")
        
        # 测试蓝球转移矩阵
        blue_matrix = analyzer.calculate_blue_ball_transition_matrix()
        print(f"✓ 蓝球转移矩阵计算成功，形状: {blue_matrix.shape}")
        
        # 验证概率矩阵性质（每列的概率和应该为1）
        for j in range(16):
            col_sum = np.sum(blue_matrix[:, j])
            if not np.isclose(col_sum, 1.0, atol=1e-6):
                print(f"✗ 蓝球转移矩阵第{j+1}列概率和不为1: {col_sum}")
                return False
        print("✓ 蓝球转移矩阵概率验证通过（每列概率和为1）")
        
        return True, analyzer
    except Exception as e:
        print(f"✗ 马尔科夫分析器测试失败: {e}")
        traceback.print_exc()
        return False, None


def test_number_selector_with_markov(small_db, large_db, original_db, parameters):
    """测试包含马尔科夫功能的号码选择器"""
    print("\n=== 测试号码选择器（含马尔科夫） ===")
    try:
        selector = NumberSelector(small_db, large_db, original_db, parameters)
        
        # 测试第3组号码（马尔科夫）
        group3_red, group3_blue = selector.get_group3_markov_numbers(15, 5)
        print(f"✓ 第3组号码（马尔科夫）获取成功: 红球{len(group3_red)}个，蓝球{len(group3_blue)}个")
        
        # 测试第4组号码（三组交集）
        group4_red, group4_blue = selector.get_group4_numbers_with_params(15, 5)
        print(f"✓ 第4组号码（三组交集）获取成功: 红球{len(group4_red)}个，蓝球{len(group4_blue)}个")
        
        # 测试马尔科夫概率计算
        red_markov_df, blue_markov_df = selector.calculate_markov_probabilities()
        print(f"✓ 马尔科夫概率计算成功: 红球{len(red_markov_df)}行，蓝球{len(blue_markov_df)}行")
        
        # 验证概率和
        red_prob_sum = red_markov_df['号码迁移概率'].sum()
        blue_prob_sum = blue_markov_df['号码迁移概率'].sum()
        
        if not np.isclose(red_prob_sum, 1.0, atol=1e-6):
            print(f"✗ 红球马尔科夫概率和不为1: {red_prob_sum}")
            return False
        
        if not np.isclose(blue_prob_sum, 1.0, atol=1e-6):
            print(f"✗ 蓝球马尔科夫概率和不为1: {blue_prob_sum}")
            return False
        
        print("✓ 马尔科夫概率和验证通过")
        
        return True, selector, group4_red, group4_blue
    except Exception as e:
        print(f"✗ 号码选择器测试失败: {e}")
        traceback.print_exc()
        return False, None, None, None


def test_enhanced_result_saver():
    """测试增强的结果保存器"""
    print("\n=== 测试增强的结果保存器 ===")
    try:
        saver = ResultSaver()
        
        # 测试新的文件名生成
        filename = saver.generate_filename(25096, 10, 50, 20, 10)
        expected = "Result_SSQ_25096_10_50_20_10_20250825.xlsx"
        
        if filename.startswith("Result_SSQ_25096_10_50_20_10_"):
            print(f"✓ 文件名生成成功: {filename}")
        else:
            print(f"✗ 文件名格式错误: {filename}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ 结果保存器测试失败: {e}")
        return False


def test_complete_workflow():
    """测试完整工作流程"""
    print("\n=== 测试完整工作流程 ===")
    try:
        # 加载数据和参数
        loader = SSQDataLoader()
        loader.load_data()
        parameters = loader.load_parameters()
        
        # 获取数据库
        small_db = loader.get_current_database(5)
        large_db = loader.get_current_database(20)
        
        # 创建选择器
        selector = NumberSelector(small_db, large_db, loader.ssqhistory_all, parameters)
        
        # 获取第4组号码
        final_red_balls, final_blue_balls = selector.get_group4_numbers_with_params(15, 6)
        
        if len(final_red_balls) < 6 or len(final_blue_balls) == 0:
            print("跳过完整工作流程测试：交集不足")
            return True
        
        # 限制测试规模
        if len(final_red_balls) > 8:
            final_red_balls = set(sorted(list(final_red_balls))[:8])
        
        # 筛选号码
        filtered_combinations = selector.filter_combinations(final_red_balls, final_blue_balls)
        
        if len(filtered_combinations) == 0:
            print("跳过完整工作流程测试：没有符合条件的组合")
            return True
        
        # 分析组合
        results_df = selector.analyze_filtered_combinations(filtered_combinations)
        
        # 计算概率
        red_prob_df, blue_prob_df = selector.calculate_large_db_probabilities()
        red_transition_df, blue_transition_df = selector.calculate_transition_tables()
        red_markov_df, blue_markov_df = selector.calculate_markov_probabilities()

        # 保存结果
        saver = ResultSaver()
        filepath = saver.save_results(
            results_df, red_prob_df, blue_prob_df, red_transition_df, blue_transition_df,
            red_markov_df, blue_markov_df, 25096, 5, 20, 15, 6
        )
        
        print(f"✓ 完整工作流程测试成功")
        print(f"✓ 生成了{len(results_df)}组符合条件的号码")
        print(f"✓ 结果保存到: {filepath}")
        
        # 验证Excel文件
        xl_file = pd.ExcelFile(filepath)
        expected_sheets = ['详细结果', '红球历史出现概率', '蓝球历史出现概率',
                          '红球号码历史跟随性概率', '蓝球号码历史跟随性概率',
                          '红球号码迁移概率', '蓝球号码迁移概率', '汇总信息']
        
        for sheet in expected_sheets:
            if sheet in xl_file.sheet_names:
                print(f"✓ 标签页存在: {sheet}")
            else:
                print(f"✗ 标签页缺失: {sheet}")
                return False
        
        return True
    except Exception as e:
        print(f"✗ 完整工作流程测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("最终版SSQ选号程序功能测试")
    print("=" * 60)
    
    all_passed = True
    
    # 测试数据加载器（含参数）
    success, loader, parameters = test_data_loader_with_parameters()
    all_passed &= success
    
    if not success:
        print("\n测试终止：数据加载器测试失败")
        return False
    
    # 获取测试数据
    small_db = loader.get_current_database(5)
    large_db = loader.get_current_database(20)
    
    # 测试马尔科夫分析器
    success, analyzer = test_markov_analyzer(large_db)
    all_passed &= success
    
    # 测试号码选择器（含马尔科夫）
    if success:
        success, selector, red_balls, blue_balls = test_number_selector_with_markov(
            small_db, large_db, loader.ssqhistory_all, parameters
        )
        all_passed &= success
    
    # 测试结果保存器
    success = test_enhanced_result_saver()
    all_passed &= success
    
    # 测试完整工作流程
    success = test_complete_workflow()
    all_passed &= success
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✓ 所有测试通过！最终版程序功能正常。")
        print("\n新增功能验证:")
        print("✓ 马尔科夫链算法")
        print("✓ 历史跟随性概率计算")
        print("✓ Parameters标签页参数读取")
        print("✓ 三组号码交集计算")
        print("✓ 增强的筛选条件")
        print("✓ 扩展的输出格式")
        print("✓ 新的文件命名规则")
    else:
        print("✗ 部分测试失败，请检查程序。")
    
    return all_passed


if __name__ == "__main__":
    main()
