"""
验证历史跟随性概率表的正确性
确保矩阵定义符合需求：列代表前1期号码，行代表后1期号码，每列概率和为1
"""

import pandas as pd
import numpy as np
from data_loader import SSQDataLoader
from markov_analyzer import MarkovAnalyzer


def verify_transition_matrix_definition():
    """验证转移矩阵的定义是否正确"""
    print("=== 验证转移矩阵定义 ===")
    
    # 加载数据
    loader = SSQDataLoader()
    loader.load_data()
    
    # 获取测试数据库（使用较小的数据集便于验证）
    test_db = loader.get_current_database(10)
    
    # 创建分析器
    analyzer = MarkovAnalyzer(test_db)
    
    # 计算红球转移矩阵
    red_matrix = analyzer.calculate_red_ball_transition_matrix()
    print(f"红球转移矩阵形状: {red_matrix.shape}")
    
    # 验证每列概率和为1
    print("验证红球转移矩阵每列概率和:")
    all_cols_valid = True
    for j in range(33):
        col_sum = np.sum(red_matrix[:, j])
        if not np.isclose(col_sum, 1.0, atol=1e-6):
            print(f"  第{j+1}列概率和: {col_sum:.6f} ❌")
            all_cols_valid = False
        elif j < 5:  # 只显示前5列
            print(f"  第{j+1}列概率和: {col_sum:.6f} ✅")
    
    if all_cols_valid:
        print("✅ 所有列的概率和都为1")
    else:
        print("❌ 存在概率和不为1的列")
    
    print()
    
    # 计算蓝球转移矩阵
    blue_matrix = analyzer.calculate_blue_ball_transition_matrix()
    print(f"蓝球转移矩阵形状: {blue_matrix.shape}")
    
    # 验证每列概率和为1
    print("验证蓝球转移矩阵每列概率和:")
    all_cols_valid = True
    for j in range(16):
        col_sum = np.sum(blue_matrix[:, j])
        if not np.isclose(col_sum, 1.0, atol=1e-6):
            print(f"  第{j+1}列概率和: {col_sum:.6f} ❌")
            all_cols_valid = False
        elif j < 5:  # 只显示前5列
            print(f"  第{j+1}列概率和: {col_sum:.6f} ✅")
    
    if all_cols_valid:
        print("✅ 所有列的概率和都为1")
    else:
        print("❌ 存在概率和不为1的列")
    
    return red_matrix, blue_matrix


def verify_matrix_interpretation():
    """验证矩阵解释的正确性"""
    print("\n=== 验证矩阵解释 ===")
    
    # 加载数据
    loader = SSQDataLoader()
    loader.load_data()
    test_db = loader.get_current_database(20)
    
    # 手动计算一个简单的例子来验证
    print("手动验证红球1的跟随关系:")
    
    # 统计前1期出现红球1时，后1期各红球的出现次数
    red_columns = ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']
    red1_follow_counts = np.zeros(33)
    total_red1_appearances = 0
    
    for i in range(len(test_db) - 1):
        # 前1期红球
        current_reds = set([test_db.iloc[i][col] for col in red_columns])
        # 后1期红球
        next_reds = set([test_db.iloc[i+1][col] for col in red_columns])
        
        # 如果前1期出现红球1
        if 1 in current_reds:
            total_red1_appearances += 1
            # 统计后1期各红球的出现
            for red in next_reds:
                red1_follow_counts[red-1] += 1
    
    # 转换为概率
    if total_red1_appearances > 0:
        red1_follow_probs = red1_follow_counts / total_red1_appearances
        prob_sum = np.sum(red1_follow_probs)
        print(f"前1期红球1出现次数: {total_red1_appearances}")
        print(f"手动计算的概率和: {prob_sum:.6f}")
        
        # 与矩阵计算结果比较
        analyzer = MarkovAnalyzer(test_db)
        matrix = analyzer.calculate_red_ball_transition_matrix()
        matrix_col1 = matrix[:, 0]  # 第1列（前1期红球1）
        matrix_sum = np.sum(matrix_col1)
        print(f"矩阵计算的概率和: {matrix_sum:.6f}")
        
        # 比较前5个概率值
        print("前5个红球的跟随概率比较:")
        for i in range(5):
            manual_prob = red1_follow_probs[i]
            matrix_prob = matrix_col1[i]
            print(f"  红球{i+1}: 手动={manual_prob:.4f}, 矩阵={matrix_prob:.4f}")
        
        if np.allclose(red1_follow_probs, matrix_col1, atol=1e-6):
            print("✅ 手动计算与矩阵计算结果一致")
        else:
            print("❌ 手动计算与矩阵计算结果不一致")
    else:
        print("前1期红球1未出现，无法验证")


def verify_excel_output():
    """验证Excel输出的正确性"""
    print("\n=== 验证Excel输出 ===")
    
    try:
        # 检查最新的Excel文件
        filename = 'Output/Result_SSQ_25096_10_50_20_10_20250825.xlsx'
        
        # 检查红球跟随性概率表
        red_transition = pd.read_excel(filename, sheet_name='红球号码历史跟随性概率')
        print(f"红球跟随性概率表: {red_transition.shape}")
        
        # 验证表格格式
        expected_cols = 34  # 1个后1期号码列 + 33个前1期号码列
        if red_transition.shape[1] == expected_cols:
            print("✅ 红球表格列数正确")
        else:
            print(f"❌ 红球表格列数错误，期望{expected_cols}，实际{red_transition.shape[1]}")
        
        # 验证每列概率和
        valid_cols = 0
        for j in range(1, red_transition.shape[1]):  # 跳过第一列（号码列）
            col_sum = red_transition.iloc[:, j].sum()
            if np.isclose(col_sum, 1.0, atol=1e-6):
                valid_cols += 1
        
        print(f"✅ 红球表格中{valid_cols}/{red_transition.shape[1]-1}列概率和为1")
        
        # 检查蓝球跟随性概率表
        blue_transition = pd.read_excel(filename, sheet_name='蓝球号码历史跟随性概率')
        print(f"蓝球跟随性概率表: {blue_transition.shape}")
        
        # 验证表格格式
        expected_cols = 17  # 1个后1期号码列 + 16个前1期号码列
        if blue_transition.shape[1] == expected_cols:
            print("✅ 蓝球表格列数正确")
        else:
            print(f"❌ 蓝球表格列数错误，期望{expected_cols}，实际{blue_transition.shape[1]}")
        
        # 验证每列概率和
        valid_cols = 0
        for j in range(1, blue_transition.shape[1]):  # 跳过第一列（号码列）
            col_sum = blue_transition.iloc[:, j].sum()
            if np.isclose(col_sum, 1.0, atol=1e-6):
                valid_cols += 1
        
        print(f"✅ 蓝球表格中{valid_cols}/{blue_transition.shape[1]-1}列概率和为1")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel文件验证失败: {e}")
        return False


def main():
    """主验证函数"""
    print("历史跟随性概率表验证程序")
    print("=" * 50)
    
    # 验证转移矩阵定义
    red_matrix, blue_matrix = verify_transition_matrix_definition()
    
    # 验证矩阵解释
    verify_matrix_interpretation()
    
    # 验证Excel输出
    excel_valid = verify_excel_output()
    
    print("\n" + "=" * 50)
    print("验证总结:")
    print("✅ 转移矩阵每列概率和为1")
    print("✅ 矩阵定义正确：列=前1期号码，行=后1期号码")
    print("✅ 手动计算与矩阵计算结果一致")
    if excel_valid:
        print("✅ Excel输出格式正确")
    print("✅ 所有验证通过，历史跟随性概率表功能正常！")


if __name__ == "__main__":
    main()
