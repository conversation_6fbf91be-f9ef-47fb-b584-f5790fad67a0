"""
数据读取模块
负责从Excel文件中读取SSQ历史数据
"""

import pandas as pd
import numpy as np
from typing import Tuple, List, Dict


class SSQDataLoader:
    """SSQ数据加载器"""
    
    def __init__(self, file_path: str = "lottery_data_all.xlsx"):
        """
        初始化数据加载器

        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.ssqhistory_all = None
        self.parameters = None
    
    def load_data(self) -> pd.DataFrame:
        """
        读取SSQ历史数据

        Returns:
            包含期号和红蓝球号码的DataFrame
        """
        try:
            # 读取Excel文件中的SSQ_data_all标签页
            df = pd.read_excel(self.file_path, sheet_name='SSQ_data_all')

            # 根据实际数据结构选择列
            # NO列：期号，r1-r6列：红球1-6（已排序），b列：蓝球
            # 使用位置索引：NO(0), r1-r6(8-13), b(14)
            selected_columns = [0] + list(range(8, 15))
            df_selected = df.iloc[:, selected_columns]
            df_selected.columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球']

            # 清除无效数据和空数据行
            df_selected = df_selected.dropna()

            # 确保期号为整数类型
            df_selected['期号'] = df_selected['期号'].astype(int)

            # 确保红蓝球号码为整数类型
            for col in ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球']:
                df_selected[col] = df_selected[col].astype(int)

            # 按期号从小到大排序
            df_selected = df_selected.sort_values('期号').reset_index(drop=True)

            self.ssqhistory_all = df_selected

            print(f"成功读取SSQ历史数据，共{len(df_selected)}期")
            return df_selected

        except Exception as e:
            print(f"读取数据时发生错误: {e}")
            raise
    
    def get_current_database(self, periods: int) -> pd.DataFrame:
        """
        根据用户输入的期数获取当前数据库
        
        Args:
            periods: 要获取的期数
            
        Returns:
            当前数据库DataFrame
        """
        if self.ssqhistory_all is None:
            raise ValueError("请先加载数据")
        
        if periods <= 0:
            raise ValueError("期数必须为正整数")
        
        if periods > len(self.ssqhistory_all):
            print(f"警告：请求的期数({periods})超过了历史数据总期数({len(self.ssqhistory_all)})，将使用全部历史数据")
            return self.ssqhistory_all.copy()
        
        # 获取最新的periods期数据
        current_db = self.ssqhistory_all.tail(periods).copy()
        
        print(f"当前数据库包含最新{len(current_db)}期数据")
        print(f"期号范围：{current_db['期号'].min()} - {current_db['期号'].max()}")
        
        return current_db
    
    def get_latest_period_info(self) -> Tuple[int, List[int], int]:
        """
        获取最新一期的信息
        
        Returns:
            (期号, 红球号码列表, 蓝球号码)
        """
        if self.ssqhistory_all is None:
            raise ValueError("请先加载数据")
        
        latest_row = self.ssqhistory_all.iloc[-1]
        period_no = latest_row['期号']
        red_balls = [latest_row[f'红球{i}'] for i in range(1, 7)]
        blue_ball = latest_row['蓝球']
        
        return period_no, red_balls, blue_ball

    def load_parameters(self) -> Dict[str, int]:
        """
        从Parameters标签页读取筛选参数

        Returns:
            包含筛选参数的字典
        """
        try:
            # 读取Parameters标签页的B2:C6区域
            params_df = pd.read_excel(self.file_path, sheet_name='Parameters',
                                    usecols=[1, 2], skiprows=1, nrows=5, header=None)

            # 提取参数值
            parameters = {
                'NumRB_min': int(params_df.iloc[0, 0]),  # B2
                'NumRB_max': int(params_df.iloc[0, 1]),  # C2
                'NumRQ_min': int(params_df.iloc[1, 0]),  # B3
                'NumRQ_max': int(params_df.iloc[1, 1]),  # C3
                'NumRZ_min': int(params_df.iloc[2, 0]),  # B4
                'NumRZ_max': int(params_df.iloc[2, 1]),  # C4
                'SumR_min': int(params_df.iloc[3, 0]),   # B5
                'SumR_max': int(params_df.iloc[3, 1]),   # C5
                'NumRAC_min': int(params_df.iloc[4, 0]), # B6
                'NumRAC_max': int(params_df.iloc[4, 1])  # C6
            }

            self.parameters = parameters

            print("成功读取筛选参数")

            return parameters

        except Exception as e:
            print(f"读取Parameters标签页时发生错误: {e}")
            # 使用默认参数
            default_parameters = {
                'NumRB_min': 3, 'NumRB_max': 4,
                'NumRQ_min': 3, 'NumRQ_max': 4,
                'NumRZ_min': 2, 'NumRZ_max': 3,
                'SumR_min': 80, 'SumR_max': 119,
                'NumRAC_min': 7, 'NumRAC_max': 8
            }
            print("使用默认筛选参数:")
            for key, value in default_parameters.items():
                print(f"  {key}: {value}")

            self.parameters = default_parameters
            return default_parameters
