"""
SSQ批量测试程序
基于main.py主程序，测试每期预测的第4组复式红蓝球号码的最大命中情况
"""

import sys
import os
import traceback
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Tuple, List, Set, Dict, Optional
from data_loader import SSQDataLoader
from number_selector import NumberSelector


class SSQBatchTester:
    """SSQ批量测试器"""
    
    def __init__(self, file_path: str = "lottery_data_all.xlsx"):
        """
        初始化批量测试器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.ssqhistory_all = None
        self.parameters = None
        self.results = []
        
    def load_data(self) -> pd.DataFrame:
        """
        读取SSQ历史数据
        
        Returns:
            包含期号和红蓝球号码的DataFrame
        """
        try:
            # 读取Excel文件中的SSQ_data_all标签页
            df = pd.read_excel(self.file_path, sheet_name='SSQ_data_all')
            
            # 根据实际数据结构选择列：A列、I列至O列（共8列数据）
            # A列：期号(0)，I-N列：红球1-6(8-13)，O列：蓝球(14)
            selected_columns = [0] + list(range(8, 15))
            df_selected = df.iloc[:, selected_columns]
            df_selected.columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球']
            
            # 清除无效数据和空数据行
            df_selected = df_selected.dropna()
            
            # 确保期号为整数类型
            df_selected['期号'] = df_selected['期号'].astype(int)
            
            # 确保红蓝球号码为整数类型
            for col in ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球']:
                df_selected[col] = df_selected[col].astype(int)
            
            # 按期号从小到大排序
            df_selected = df_selected.sort_values('期号').reset_index(drop=True)
            
            self.ssqhistory_all = df_selected
            
            print(f"成功读取SSQ历史数据，共{len(df_selected)}期")
            return df_selected
            
        except Exception as e:
            print(f"读取数据时发生错误: {e}")
            raise
    
    def load_parameters(self) -> Dict[str, int]:
        """
        从Parameters标签页读取筛选参数
        
        Returns:
            包含筛选参数的字典
        """
        try:
            # 读取Parameters标签页的B2:C6区域
            params_df = pd.read_excel(self.file_path, sheet_name='Parameters',
                                    usecols=[1, 2], skiprows=1, nrows=5, header=None)
            
            # 提取参数值
            parameters = {
                'NumRB_min': int(params_df.iloc[0, 0]),  # B2
                'NumRB_max': int(params_df.iloc[0, 1]),  # C2
                'NumRQ_min': int(params_df.iloc[1, 0]),  # B3
                'NumRQ_max': int(params_df.iloc[1, 1]),  # C3
                'NumRZ_min': int(params_df.iloc[2, 0]),  # B4
                'NumRZ_max': int(params_df.iloc[2, 1]),  # C4
                'SumR_min': int(params_df.iloc[3, 0]),   # B5
                'SumR_max': int(params_df.iloc[3, 1]),   # C5
                'NumRAC_min': int(params_df.iloc[4, 0]), # B6
                'NumRAC_max': int(params_df.iloc[4, 1])  # C6
            }
            
            self.parameters = parameters
            
            print("成功读取筛选参数:")
            for key, value in parameters.items():
                print(f"  {key}: {value}")
            
            return parameters
            
        except Exception as e:
            print(f"读取Parameters标签页时发生错误: {e}")
            # 使用默认参数
            default_parameters = {
                'NumRB_min': 3, 'NumRB_max': 4,
                'NumRQ_min': 3, 'NumRQ_max': 4,
                'NumRZ_min': 2, 'NumRZ_max': 3,
                'SumR_min': 80, 'SumR_max': 119,
                'NumRAC_min': 7, 'NumRAC_max': 8
            }
            print("使用默认筛选参数:")
            for key, value in default_parameters.items():
                print(f"  {key}: {value}")
            
            self.parameters = default_parameters
            return default_parameters
    
    def get_user_input(self) -> Tuple[int, int, int, int, int, int]:
        """
        获取用户输入的参数
        
        Returns:
            (起始期号, 小数据库期数, 大数据库期数, 红球数量NR, 蓝球数量NB, 答案数据库期数)
        """
        while True:
            try:
                # 获取起始期号
                start_period_input = input("\n请输入起始期号（如：23001）: ").strip()
                start_period = int(start_period_input)
                
                if start_period <= 0:
                    print("错误：期号必须为正整数，请重新输入。")
                    continue
                
                # 检查起始期号是否存在于数据库中
                if start_period not in self.ssqhistory_all['期号'].values:
                    print(f"错误：期号{start_period}不存在于数据库中，请重新输入。")
                    continue
                
                # 获取小数据库期数
                small_input = input("请输入当前数据库（小）的期数（正整数）: ").strip()
                small_periods = int(small_input)
                
                if small_periods <= 0:
                    print("错误：期数必须为正整数，请重新输入。")
                    continue
                
                # 获取大数据库期数
                large_input = input("请输入当前数据库（大）的期数（正整数）: ").strip()
                large_periods = int(large_input)
                
                if large_periods <= 0:
                    print("错误：期数必须为正整数，请重新输入。")
                    continue
                
                if large_periods < small_periods:
                    print("错误：大数据库期数应该大于等于小数据库期数，请重新输入。")
                    continue
                
                # 获取红球和蓝球数量
                while True:
                    try:
                        nr_nb_input = input("请输入想要选择的红球数量和蓝球数量（用逗号隔开，如：20,6）: ").strip()
                        nr_str, nb_str = nr_nb_input.split(',')
                        nr = int(nr_str.strip())
                        nb = int(nb_str.strip())
                        
                        if nr <= 0 or nb <= 0:
                            print("错误：红球和蓝球数量必须为正整数，请重新输入。")
                            continue
                        
                        if nr < 6:
                            print("错误：红球数量不能少于6个（需要组成6红球+1蓝球的组合），请重新输入。")
                            continue
                        
                        if nb < 1:
                            print("错误：蓝球数量不能少于1个，请重新输入。")
                            continue
                        
                        if nr > 33:
                            print("错误：红球数量不能超过33个（红球总数），请重新输入。")
                            continue
                        
                        if nb > 16:
                            print("错误：蓝球数量不能超过16个（蓝球总数），请重新输入。")
                            continue
                        
                        break
                        
                    except ValueError:
                        print("错误：请输入正确的格式，如：20,6")
                        continue
                
                # 获取答案数据库期数
                answer_input = input("请输入答案数据库的期数（正整数）: ").strip()
                answer_periods = int(answer_input)
                
                if answer_periods <= 0:
                    print("错误：期数必须为正整数，请重新输入。")
                    continue
                
                return start_period, small_periods, large_periods, nr, nb, answer_periods

            except ValueError:
                print("错误：请输入有效的正整数。")
            except KeyboardInterrupt:
                print("\n程序已取消。")
                sys.exit(0)

    def get_database_by_period_range(self, start_period: int, periods: int, direction: str = "before") -> pd.DataFrame:
        """
        根据起始期号和期数获取数据库

        Args:
            start_period: 起始期号
            periods: 期数
            direction: 方向，"before"表示起始期号及之前的数据，"after"表示起始期号之后的数据

        Returns:
            数据库DataFrame
        """
        if self.ssqhistory_all is None:
            raise ValueError("请先加载数据")

        # 找到起始期号在数据库中的位置
        period_index = self.ssqhistory_all[self.ssqhistory_all['期号'] == start_period].index
        if len(period_index) == 0:
            raise ValueError(f"期号{start_period}不存在于数据库中")

        period_idx = period_index[0]

        if direction == "before":
            # 获取起始期号及之前的periods期数据
            start_idx = max(0, period_idx - periods + 1)
            end_idx = period_idx + 1
            return self.ssqhistory_all.iloc[start_idx:end_idx].copy()
        elif direction == "after":
            # 获取起始期号之后的periods期数据（不包括起始期号）
            start_idx = period_idx + 1
            end_idx = min(len(self.ssqhistory_all), start_idx + periods)
            return self.ssqhistory_all.iloc[start_idx:end_idx].copy()
        else:
            raise ValueError("direction参数必须为'before'或'after'")

    def get_next_period(self, current_period: int) -> Optional[int]:
        """
        获取当前期号的下一期期号，处理年份跨越的情况

        Args:
            current_period: 当前期号

        Returns:
            下一期期号，如果没有下一期则返回None
        """
        if self.ssqhistory_all is None:
            raise ValueError("请先加载数据")

        # 找到当前期号在数据库中的位置
        period_index = self.ssqhistory_all[self.ssqhistory_all['期号'] == current_period].index
        if len(period_index) == 0:
            return None

        period_idx = period_index[0]

        # 检查是否有下一期
        if period_idx + 1 >= len(self.ssqhistory_all):
            return None

        # 返回下一期的期号
        return self.ssqhistory_all.iloc[period_idx + 1]['期号']

    def calculate_hit_rate(self, predicted_red: Set[int], predicted_blue: Set[int],
                          answer_db: pd.DataFrame) -> Tuple[int, int, int]:
        """
        计算复式红蓝球号码与答案数据库的最大命中情况

        Args:
            predicted_red: 预测的红球号码集合
            predicted_blue: 预测的蓝球号码集合
            answer_db: 答案数据库

        Returns:
            (最大命中数, 中奖期号, 最大命中次数)
        """
        max_hit = 0
        winning_period = 0
        max_hit_count = 0

        # 处理蓝球号码为空的情况
        if len(predicted_blue) == 0:
            predicted_blue = {0}

        for _, row in answer_db.iterrows():
            period = row['期号']
            answer_red = {row['红球1'], row['红球2'], row['红球3'],
                         row['红球4'], row['红球5'], row['红球6']}
            answer_blue = {row['蓝球']}

            # 计算红球命中数
            red_hits = len(predicted_red & answer_red)

            # 计算蓝球命中数
            blue_hits = len(predicted_blue & answer_blue) if 0 not in predicted_blue else 0

            # 总命中数
            total_hits = red_hits + blue_hits

            # 更新最大命中情况
            if total_hits > max_hit:
                max_hit = total_hits
                winning_period = period
                max_hit_count = 1
            elif total_hits == max_hit and total_hits > 0:
                # 如果有多个最大命中，选择期号最小的
                if period < winning_period:
                    winning_period = period
                max_hit_count += 1

        return max_hit, winning_period, max_hit_count

    def get_winning_numbers(self, winning_period: int) -> Tuple[str, str]:
        """
        获取中奖期号对应的红蓝球号码

        Args:
            winning_period: 中奖期号

        Returns:
            (红球号码字符串, 蓝球号码字符串)
        """
        if winning_period == 0 or self.ssqhistory_all is None:
            return "", ""

        # 在历史数据中查找中奖期号
        winning_row = self.ssqhistory_all[self.ssqhistory_all['期号'] == winning_period]
        if len(winning_row) == 0:
            return "", ""

        row = winning_row.iloc[0]
        red_balls = [row['红球1'], row['红球2'], row['红球3'], row['红球4'], row['红球5'], row['红球6']]
        blue_ball = row['蓝球']

        red_str = ','.join([f'{x:02d}' for x in sorted(red_balls)])
        blue_str = f'{blue_ball:02d}'

        return red_str, blue_str

    def get_group2_sorted_numbers(self, large_db: pd.DataFrame, nr: int, nb: int) -> Tuple[List[int], List[int]]:
        """
        获取第2组按历史出现概率排序的红蓝球号码

        Args:
            large_db: 大数据库
            nr: 红球数量
            nb: 蓝球数量

        Returns:
            (按概率排序的红球号码列表, 按概率排序的蓝球号码列表)
        """
        # 统计红球出现次数
        red_counts = {}
        for _, row in large_db.iterrows():
            for col in ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']:
                ball = row[col]
                red_counts[ball] = red_counts.get(ball, 0) + 1

        # 统计蓝球出现次数
        blue_counts = {}
        for _, row in large_db.iterrows():
            ball = row['蓝球']
            blue_counts[ball] = blue_counts.get(ball, 0) + 1

        # 计算总数用于概率计算
        total_red_count = sum(red_counts.values())
        total_blue_count = len(large_db)

        # 转换为概率并按要求排序：概率从大到小，概率相等时号码从小到大
        red_probs = [(ball, count / total_red_count) for ball, count in red_counts.items()]
        blue_probs = [(ball, count / total_blue_count) for ball, count in blue_counts.items()]

        # 排序：先按概率降序，概率相等时按号码升序
        sorted_red_balls = sorted(red_probs, key=lambda x: (-x[1], x[0]))
        sorted_blue_balls = sorted(blue_probs, key=lambda x: (-x[1], x[0]))

        # 取前nr个红球和前nb个蓝球
        top_red_balls = [ball for ball, prob in sorted_red_balls[:nr]]
        top_blue_balls = [ball for ball, prob in sorted_blue_balls[:nb]]

        return top_red_balls, top_blue_balls

    def get_group3_sorted_numbers(self, large_db: pd.DataFrame, nr: int, nb: int) -> Tuple[List[int], List[int]]:
        """
        获取第3组按号码迁移概率排序的红蓝球号码

        Args:
            large_db: 大数据库
            nr: 红球数量
            nb: 蓝球数量

        Returns:
            (按迁移概率排序的红球号码列表, 按迁移概率排序的蓝球号码列表)
        """
        try:
            # 动态导入马尔科夫分析器
            from markov_analyzer import MarkovAnalyzer
            markov_analyzer = MarkovAnalyzer(large_db)

            # 获取最新一期的红蓝球号码
            latest_row = large_db.iloc[-1]
            latest_red_balls = [latest_row[f'红球{i}'] for i in range(1, 7)]
            latest_blue_ball = latest_row['蓝球']

            # 计算转移概率矩阵
            red_transition_matrix = markov_analyzer.calculate_red_ball_transition_matrix()
            blue_transition_matrix = markov_analyzer.calculate_blue_ball_transition_matrix()

            # 计算红球历史出现概率（用于马尔科夫计算）
            red_counts = {}
            for _, row in large_db.iterrows():
                for col in ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']:
                    ball = row[col]
                    red_counts[ball] = red_counts.get(ball, 0) + 1

            # 转换为概率DataFrame
            total_red_count = sum(red_counts.values())
            red_historical_probs = pd.DataFrame([
                {'红球号码': ball, '历史出现概率': count / total_red_count}
                for ball, count in red_counts.items()
            ])

            # 计算马尔科夫概率
            red_markov_df, blue_markov_df = markov_analyzer.calculate_markov_probabilities(
                latest_red_balls, latest_blue_ball, red_transition_matrix, blue_transition_matrix, red_historical_probs
            )

            # 按迁移概率从大到小排序，概率相等时按号码从小到大排序
            red_markov_sorted = red_markov_df.sort_values(['号码迁移概率', '红球号码'], ascending=[False, True])
            blue_markov_sorted = blue_markov_df.sort_values(['号码迁移概率', '蓝球号码'], ascending=[False, True])

            top_red_balls = red_markov_sorted.head(nr)['红球号码'].tolist()
            top_blue_balls = blue_markov_sorted.head(nb)['蓝球号码'].tolist()

            return top_red_balls, top_blue_balls

        except Exception as e:
            print(f"马尔科夫计算失败，使用历史概率替代: {e}")
            # 如果马尔科夫计算失败，回退到历史概率
            return self.get_group2_sorted_numbers(large_db, nr, nb)

    def run_batch_test(self, start_period: int, small_periods: int, large_periods: int,
                      nr: int, nb: int, answer_periods: int) -> List[Dict]:
        """
        运行批量测试

        Args:
            start_period: 起始期号
            small_periods: 小数据库期数
            large_periods: 大数据库期数
            nr: 红球数量
            nb: 蓝球数量
            answer_periods: 答案数据库期数

        Returns:
            测试结果列表
        """
        results = []
        current_period = start_period

        # 计算需要循环运行的总期数
        total_periods = 0
        temp_period = start_period
        while temp_period is not None:
            # 检查是否有足够的答案数据
            try:
                answer_db = self.get_database_by_period_range(temp_period, answer_periods, "after")
                if len(answer_db) < answer_periods:
                    break
                total_periods += 1
                temp_period = self.get_next_period(temp_period)
            except:
                break

        print(f"\n需要循环运行的总期数: {total_periods}")

        processed_count = 0

        while current_period is not None:
            try:
                # 检查是否有足够的历史数据用于小数据库和大数据库
                small_db = self.get_database_by_period_range(current_period, small_periods, "before")
                large_db = self.get_database_by_period_range(current_period, large_periods, "before")

                if len(small_db) < small_periods or len(large_db) < large_periods:
                    print(f"期号{current_period}: 历史数据不足，跳过")
                    current_period = self.get_next_period(current_period)
                    continue

                # 检查是否有足够的答案数据
                answer_db = self.get_database_by_period_range(current_period, answer_periods, "after")
                if len(answer_db) < answer_periods:
                    print(f"期号{current_period}: 答案数据不足，测试结束")
                    break

                # 检查数据完整性
                if self.ssqhistory_all is None or self.parameters is None:
                    print(f"期号{current_period}: 数据未正确加载，跳过")
                    current_period = self.get_next_period(current_period)
                    continue

                # 创建号码选择器
                selector = NumberSelector(small_db, large_db, self.ssqhistory_all, self.parameters)

                # 获取所有4组复式红蓝球号码
                group1_red, group1_blue = selector.get_group1_numbers()  # 小数据库去重
                group4_red, group4_blue = selector.get_group4_numbers_with_params(nr, nb)  # 交集

                # 获取排序后的第2组和第3组号码
                group2_red_sorted, group2_blue_sorted = self.get_group2_sorted_numbers(large_db, nr, nb)
                group3_red_sorted, group3_blue_sorted = self.get_group3_sorted_numbers(large_db, nr, nb)

                # 分别计算每组的最大命中情况
                # 第1组
                group1_max_hit, group1_winning_period, group1_max_hit_count = self.calculate_hit_rate(
                    group1_red, group1_blue, answer_db)
                group1_winning_red, group1_winning_blue = self.get_winning_numbers(group1_winning_period)

                # 第2组
                group2_red_set = set(group2_red_sorted)
                group2_blue_set = set(group2_blue_sorted)
                group2_max_hit, group2_winning_period, group2_max_hit_count = self.calculate_hit_rate(
                    group2_red_set, group2_blue_set, answer_db)
                group2_winning_red, group2_winning_blue = self.get_winning_numbers(group2_winning_period)

                # 第3组
                group3_red_set = set(group3_red_sorted)
                group3_blue_set = set(group3_blue_sorted)
                group3_max_hit, group3_winning_period, group3_max_hit_count = self.calculate_hit_rate(
                    group3_red_set, group3_blue_set, answer_db)
                group3_winning_red, group3_winning_blue = self.get_winning_numbers(group3_winning_period)

                # 第4组
                group4_max_hit, group4_winning_period, group4_max_hit_count = self.calculate_hit_rate(
                    group4_red, group4_blue, answer_db)
                group4_winning_red, group4_winning_blue = self.get_winning_numbers(group4_winning_period)

                # 保存结果 - 每期对应4行数据
                period_results = []

                # 第1组复式红蓝球号码
                group1_blue_str = ','.join([f'{x:02d}' for x in sorted(group1_blue)]) if len(group1_blue) > 0 and 0 not in group1_blue else ''
                period_results.append({
                    '期号': current_period,
                    '组别': '第1组',
                    '红球号码': ','.join([f'{x:02d}' for x in sorted(group1_red)]),
                    '蓝球号码': group1_blue_str,
                    '最大命中情况': group1_max_hit,
                    '最大命中情况的次数': group1_max_hit_count,
                    '中奖期号': group1_winning_period,
                    '中奖红球号码': group1_winning_red,
                    '中奖蓝球号码': group1_winning_blue
                })

                # 第2组复式红蓝球号码（按历史出现概率排序，概率相等时按号码从小到大排序）
                period_results.append({
                    '期号': current_period,
                    '组别': '第2组',
                    '红球号码': ','.join([f'{x:02d}' for x in group2_red_sorted]),
                    '蓝球号码': ','.join([f'{x:02d}' for x in group2_blue_sorted]) if len(group2_blue_sorted) > 0 else '',
                    '最大命中情况': group2_max_hit,
                    '最大命中情况的次数': group2_max_hit_count,
                    '中奖期号': group2_winning_period,
                    '中奖红球号码': group2_winning_red,
                    '中奖蓝球号码': group2_winning_blue
                })

                # 第3组复式红蓝球号码（按号码迁移概率排序，概率相等时按号码从小到大排序）
                period_results.append({
                    '期号': current_period,
                    '组别': '第3组',
                    '红球号码': ','.join([f'{x:02d}' for x in group3_red_sorted]),
                    '蓝球号码': ','.join([f'{x:02d}' for x in group3_blue_sorted]) if len(group3_blue_sorted) > 0 else '',
                    '最大命中情况': group3_max_hit,
                    '最大命中情况的次数': group3_max_hit_count,
                    '中奖期号': group3_winning_period,
                    '中奖红球号码': group3_winning_red,
                    '中奖蓝球号码': group3_winning_blue
                })

                # 第4组复式红蓝球号码（交集）
                # 如果第4组蓝球个数为0，将蓝球号码定义为0
                group4_blue_str = '0' if len(group4_blue) == 0 else ','.join([f'{x:02d}' for x in sorted(group4_blue)])
                period_results.append({
                    '期号': current_period,
                    '组别': '第4组',
                    '红球号码': ','.join([f'{x:02d}' for x in sorted(group4_red)]),
                    '蓝球号码': group4_blue_str,
                    '最大命中情况': group4_max_hit,
                    '最大命中情况的次数': group4_max_hit_count,
                    '中奖期号': group4_winning_period,
                    '中奖红球号码': group4_winning_red,
                    '中奖蓝球号码': group4_winning_blue
                })

                results.extend(period_results)

                processed_count += 1

                # 每100期打印一次进度
                if processed_count % 100 == 0:
                    # 获取当前起始期号对应的红蓝球号码
                    current_period_row = self.ssqhistory_all[self.ssqhistory_all['期号'] == current_period]
                    if len(current_period_row) > 0:
                        row = current_period_row.iloc[0]
                        current_red = [row['红球1'], row['红球2'], row['红球3'], row['红球4'], row['红球5'], row['红球6']]
                        current_blue = row['蓝球']
                        current_red_str = ','.join([f'{x:02d}' for x in sorted(current_red)])
                        current_blue_str = f'{current_blue:02d}'
                    else:
                        current_red_str = "未知"
                        current_blue_str = "未知"

                    print(f"已完成{processed_count}期，当前起始期号: {current_period}")
                    print(f"当前起始期号红球: {current_red_str}")
                    print(f"当前起始期号蓝球: {current_blue_str}")
                    print(f"当前期第4组红球: {period_results[3]['红球号码']}")
                    print(f"当前期第4组蓝球: {period_results[3]['蓝球号码']}")
                    print(f"当前期第4组最大命中: {period_results[3]['最大命中情况']}")

                # 获取下一期期号
                current_period = self.get_next_period(current_period)

            except Exception as e:
                print(f"处理期号{current_period}时发生错误: {e}")
                current_period = self.get_next_period(current_period)
                continue

        print(f"\n批量测试完成，共处理{processed_count}期")
        return results

    def save_results(self, results: List[Dict], small_periods: int, large_periods: int,
                    nr: int, nb: int) -> str:
        """
        保存测试结果到Excel文件

        Args:
            results: 测试结果列表（每期4行数据）
            small_periods: 小数据库期数
            large_periods: 大数据库期数
            nr: 红球数量
            nb: 蓝球数量

        Returns:
            保存的文件路径
        """
        # 确保输出目录存在
        output_dir = "Output"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 生成文件名：Results_TestMain_AAAA_BBBB_CCCC_DDDD_EEEE_FFFF
        current_date = datetime.now().strftime("%Y%m%d")
        filename = f"Results_TestMain_SSQ_{small_periods}_{large_periods}_{nr}_{nb}_{current_date}.xlsx"
        filepath = os.path.join(output_dir, filename)

        print(f"正在保存结果到文件: {filename}")

        # 转换结果为DataFrame
        results_df = pd.DataFrame(results)

        # 重新排列列的顺序
        column_order = ['期号', '组别', '红球号码', '蓝球号码', '最大命中情况',
                       '最大命中情况的次数', '中奖期号', '中奖红球号码', '中奖蓝球号码']
        results_df = results_df[column_order]

        # 创建Excel写入器
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            # 保存详细结果到"详细结果"标签页
            results_df.to_excel(writer, sheet_name='详细结果', index=False)

            # 调整列宽以便更好地显示
            worksheet = writer.sheets['详细结果']
            worksheet.column_dimensions['A'].width = 12  # 期号
            worksheet.column_dimensions['B'].width = 8   # 组别
            worksheet.column_dimensions['C'].width = 35  # 红球号码
            worksheet.column_dimensions['D'].width = 15  # 蓝球号码
            worksheet.column_dimensions['E'].width = 12  # 最大命中情况
            worksheet.column_dimensions['F'].width = 15  # 最大命中情况的次数
            worksheet.column_dimensions['G'].width = 12  # 中奖期号
            worksheet.column_dimensions['H'].width = 25  # 中奖红球号码
            worksheet.column_dimensions['I'].width = 12  # 中奖蓝球号码

        print(f"结果已保存到: {filepath}")
        return filepath


def main():
    """主函数"""
    print("=" * 60)
    print("SSQ批量测试程序")
    print("基于main.py主程序，测试每期预测的第4组复式红蓝球号码的最大命中情况")
    print("=" * 60)

    try:
        # 1. 初始化测试器并读取数据
        print("\n步骤1: 读取SSQ历史数据...")
        tester = SSQBatchTester()
        tester.load_data()

        # 读取筛选参数
        print("\n步骤1.1: 读取筛选参数...")
        tester.load_parameters()

        # 2. 用户交互 - 定义数据库范围和选号参数
        print("\n步骤2: 定义数据库范围和选号参数...")
        start_period, small_periods, large_periods, nr, nb, answer_periods = tester.get_user_input()

        print(f"\n测试参数:")
        print(f"起始期号: {start_period}")
        print(f"小数据库期数: {small_periods}")
        print(f"大数据库期数: {large_periods}")
        print(f"红球数量: {nr}")
        print(f"蓝球数量: {nb}")
        print(f"答案数据库期数: {answer_periods}")

        # 3. 运行批量测试
        print("\n步骤3: 开始批量测试...")
        results = tester.run_batch_test(start_period, small_periods, large_periods, nr, nb, answer_periods)

        if len(results) == 0:
            print("没有可处理的数据，程序结束。")
            return

        # 4. 保存结果
        print("\n步骤4: 保存测试结果...")
        filepath = tester.save_results(results, small_periods, large_periods, nr, nb)

        # 5. 显示结果摘要
        print("\n步骤5: 结果摘要...")

        # 计算实际测试的期数（每期4行数据）
        total_periods = len(results) // 4
        print(f"总共测试期数: {total_periods}")

        # 统计最大命中情况（只统计第4组的结果）
        group4_results = [r for r in results if r['组别'] == '第4组']
        hit_stats = {}
        for result in group4_results:
            hit = result['最大命中情况']
            hit_stats[hit] = hit_stats.get(hit, 0) + 1

        print("最大命中情况统计:")
        for hit in sorted(hit_stats.keys(), reverse=True):
            count = hit_stats[hit]
            percentage = count / len(group4_results) * 100
            print(f"  命中{hit}个: {count}期 ({percentage:.1f}%)")

        # 显示最高命中的几期
        if group4_results:
            max_hit = max(result['最大命中情况'] for result in group4_results)
            max_hit_results = [r for r in group4_results if r['最大命中情况'] == max_hit]

            print(f"\n最高命中({max_hit}个)的期数:")
            for result in max_hit_results[:10]:  # 只显示前10期
                print(f"  期号{result['期号']}: 红球{result['红球号码']}, 蓝球{result['蓝球号码']}, 中奖期号{result['中奖期号']}")

        print(f"\n程序执行完成！")
        print(f"结果已保存到: {filepath}")
        print("\nExcel文件包含以下标签页:")
        print("- 详细结果: 每期的4组复式红蓝球号码分析结果和命中情况")
        print("  * 每期对应4行数据，分别为第1组、第2组、第3组、第4组复式红蓝球号码")
        print("  * 第2组按历史出现概率排序，第3组按号码迁移概率排序")
        print("  * 包含中奖期号对应的红蓝球号码")

    except FileNotFoundError as e:
        print(f"错误：找不到数据文件 - {e}")
        print("请确保 lottery_data_all.xlsx 文件存在于当前目录中。")
    except Exception as e:
        print(f"程序执行过程中发生错误: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()

    input("\n按回车键退出...")


if __name__ == "__main__":
    main()
