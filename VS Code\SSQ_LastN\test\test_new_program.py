"""
新版SSQ选号程序测试脚本
用于验证修改后程序的各个功能
"""

import sys
import traceback
from data_loader import SSQDataLoader
from number_selector import NumberSelector
from result_saver import ResultSaver


def test_data_loader():
    """测试数据加载模块"""
    print("=== 测试数据加载模块 ===")
    try:
        loader = SSQDataLoader()
        data = loader.load_data()
        
        print(f"✓ 数据加载成功，共{len(data)}期")
        
        # 测试获取最新期信息
        period, red_balls, blue_ball = loader.get_latest_period_info()
        print(f"✓ 最新一期: {period}期，红球: {red_balls}，蓝球: {blue_ball}")
        
        # 测试获取小数据库和大数据库
        small_db = loader.get_current_database(3)
        large_db = loader.get_current_database(10)
        print(f"✓ 获取小数据库(3期)成功，期号范围: {small_db['期号'].min()}-{small_db['期号'].max()}")
        print(f"✓ 获取大数据库(10期)成功，期号范围: {large_db['期号'].min()}-{large_db['期号'].max()}")
        
        return True, loader, small_db, large_db, data
    except Exception as e:
        print(f"✗ 数据加载模块测试失败: {e}")
        return False, None, None, None, None


def test_number_selector(small_db, large_db, original_db):
    """测试号码选择模块"""
    print("\n=== 测试号码选择模块 ===")
    try:
        selector = NumberSelector(small_db, large_db, original_db)
        
        # 测试第1组号码
        group1_red, group1_blue = selector.get_group1_numbers()
        print(f"✓ 第1组号码获取成功: 红球{len(group1_red)}个，蓝球{len(group1_blue)}个")
        
        # 测试第2组号码
        group2_red, group2_blue = selector.get_group2_numbers()
        print(f"✓ 第2组号码获取成功: 红球{len(group2_red)}个，蓝球{len(group2_blue)}个")
        
        # 测试第3组号码（交集）
        group3_red, group3_blue = selector.get_group3_numbers_with_params(15, 5)
        print(f"✓ 第3组号码获取成功: 红球{len(group3_red)}个，蓝球{len(group3_blue)}个")
        
        # 测试概率计算
        red_prob_df, blue_prob_df = selector.calculate_large_db_probabilities()
        print(f"✓ 概率计算成功: 红球概率表{len(red_prob_df)}行，蓝球概率表{len(blue_prob_df)}行")
        
        return True, selector, group3_red, group3_blue
    except Exception as e:
        print(f"✗ 号码选择模块测试失败: {e}")
        traceback.print_exc()
        return False, None, None, None


def test_filtering(selector, red_balls, blue_balls):
    """测试筛选功能"""
    print("\n=== 测试筛选功能 ===")
    try:
        if len(red_balls) < 6:
            print(f"跳过筛选测试：红球数量({len(red_balls)})少于6个")
            return True, []
        
        if len(blue_balls) == 0:
            print("跳过筛选测试：蓝球数量为0")
            return True, []
        
        # 限制测试规模
        if len(red_balls) > 10:
            red_balls = sorted(list(red_balls))[:10]
            print(f"为了测试，限制红球数量为10个")
        
        filtered_combinations = selector.filter_combinations(red_balls, blue_balls)
        print(f"✓ 筛选功能测试成功，筛选出{len(filtered_combinations)}组合")
        
        return True, filtered_combinations
    except Exception as e:
        print(f"✗ 筛选功能测试失败: {e}")
        traceback.print_exc()
        return False, []


def test_analysis(selector, filtered_combinations):
    """测试分析功能"""
    print("\n=== 测试分析功能 ===")
    try:
        if len(filtered_combinations) == 0:
            print("跳过分析测试：没有筛选出的组合")
            return True, None
        
        # 限制测试规模
        test_combinations = filtered_combinations[:10] if len(filtered_combinations) > 10 else filtered_combinations
        
        results_df = selector.analyze_filtered_combinations(test_combinations)
        print(f"✓ 分析功能测试成功，生成{len(results_df)}行结果")
        
        # 验证结果格式
        expected_columns = ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球',
                          '红球1遗漏', '红球2遗漏', '红球3遗漏', '红球4遗漏', '红球5遗漏', '红球6遗漏', '蓝球遗漏']
        
        if list(results_df.columns) == expected_columns:
            print("✓ 结果格式验证通过")
        else:
            print(f"✗ 结果格式不匹配，期望: {expected_columns}")
            print(f"实际: {list(results_df.columns)}")
        
        return True, results_df
    except Exception as e:
        print(f"✗ 分析功能测试失败: {e}")
        traceback.print_exc()
        return False, None


def test_result_saver(results_df, red_prob_df, blue_prob_df):
    """测试结果保存功能"""
    print("\n=== 测试结果保存功能 ===")
    try:
        if results_df is None:
            print("跳过保存测试：没有结果数据")
            return True
        
        saver = ResultSaver()
        
        # 测试文件名生成
        filename = saver.generate_filename(25096, 3, 10)
        print(f"✓ 文件名生成成功: {filename}")
        
        # 测试保存功能
        filepath = saver.save_results(results_df, red_prob_df, blue_prob_df, 25096, 3, 10)
        print(f"✓ 结果保存成功: {filepath}")
        
        return True
    except Exception as e:
        print(f"✗ 结果保存功能测试失败: {e}")
        traceback.print_exc()
        return False


def run_complete_test():
    """运行完整测试"""
    print("\n=== 运行完整测试 ===")
    try:
        # 加载数据
        loader = SSQDataLoader()
        loader.load_data()
        
        # 获取小数据库和大数据库
        small_db = loader.get_current_database(3)
        large_db = loader.get_current_database(8)
        
        print(f"使用小数据库(3期)和大数据库(8期)进行测试")
        
        # 创建选择器
        selector = NumberSelector(small_db, large_db, loader.ssqhistory_all)
        
        # 获取初选号码
        final_red_balls, final_blue_balls = selector.get_group3_numbers_with_params(15, 5)
        
        if len(final_red_balls) < 6 or len(final_blue_balls) == 0:
            print("初选号码不足，跳过完整测试")
            return True
        
        # 限制测试规模
        if len(final_red_balls) > 8:
            final_red_balls = set(sorted(list(final_red_balls))[:8])
            print("为了测试，限制红球数量为8个")
        
        # 筛选号码
        filtered_combinations = selector.filter_combinations(final_red_balls, final_blue_balls)
        
        if len(filtered_combinations) == 0:
            print("没有符合条件的组合，测试完成")
            return True
        
        # 分析组合
        results_df = selector.analyze_filtered_combinations(filtered_combinations)
        
        # 计算概率
        red_prob_df, blue_prob_df = selector.calculate_large_db_probabilities()
        
        # 保存结果
        saver = ResultSaver()
        filepath = saver.save_results(results_df, red_prob_df, blue_prob_df, 25096, 3, 8)
        
        print(f"✓ 完整测试成功，结果保存到: {filepath}")
        print(f"✓ 生成了{len(results_df)}组符合条件的号码")
        
        return True
    except Exception as e:
        print(f"✗ 完整测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("新版SSQ选号程序功能测试")
    print("=" * 50)
    
    all_passed = True
    
    # 测试各个模块
    success, loader, small_db, large_db, original_db = test_data_loader()
    all_passed &= success

    if loader and small_db is not None and large_db is not None and original_db is not None:
        success, selector, red_balls, blue_balls = test_number_selector(small_db, large_db, original_db)
        all_passed &= success
        
        if selector and red_balls and blue_balls:
            success, filtered_combinations = test_filtering(selector, red_balls, blue_balls)
            all_passed &= success
            
            success, results_df = test_analysis(selector, filtered_combinations)
            all_passed &= success
            
            if results_df is not None:
                red_prob_df, blue_prob_df = selector.calculate_large_db_probabilities()
                success = test_result_saver(results_df, red_prob_df, blue_prob_df)
                all_passed &= success
    
    # 运行完整测试
    success = run_complete_test()
    all_passed &= success
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✓ 所有测试通过！新版程序功能正常。")
    else:
        print("✗ 部分测试失败，请检查程序。")
    
    return all_passed


if __name__ == "__main__":
    main()
