"""
运行完整的批量测试程序（小规模）
"""

from TestSSQ_Main import SSQBatchTester

def run_full_test():
    print("=== SSQ批量测试程序（完整版） ===")
    
    try:
        # 1. 初始化测试器并读取数据
        print("步骤1: 读取SSQ历史数据...")
        tester = SSQBatchTester()
        tester.load_data()
        
        # 读取筛选参数
        print("步骤1.1: 读取筛选参数...")
        tester.load_parameters()
        
        # 2. 使用预设参数进行测试
        print("步骤2: 使用预设参数...")
        start_period = 23001
        small_periods = 6
        large_periods = 40
        nr = 20
        nb = 6
        answer_periods = 6
        
        print(f"测试参数:")
        print(f"起始期号: {start_period}")
        print(f"小数据库期数: {small_periods}")
        print(f"大数据库期数: {large_periods}")
        print(f"红球数量: {nr}")
        print(f"蓝球数量: {nb}")
        print(f"答案数据库期数: {answer_periods}")
        
        # 3. 运行批量测试（限制期数）
        print("步骤3: 开始批量测试（测试前10期）...")
        
        results = []
        current_period = start_period
        test_count = 0
        max_test = 10
        
        while current_period is not None and test_count < max_test:
            try:
                # 检查是否有足够的历史数据
                small_db = tester.get_database_by_period_range(current_period, small_periods, "before")
                large_db = tester.get_database_by_period_range(current_period, large_periods, "before")
                
                if len(small_db) < small_periods or len(large_db) < large_periods:
                    print(f"期号{current_period}: 历史数据不足，跳过")
                    current_period = tester.get_next_period(current_period)
                    continue
                
                # 检查是否有足够的答案数据
                answer_db = tester.get_database_by_period_range(current_period, answer_periods, "after")
                if len(answer_db) < answer_periods:
                    print(f"期号{current_period}: 答案数据不足，测试结束")
                    break
                
                # 检查数据完整性
                if tester.ssqhistory_all is None or tester.parameters is None:
                    print(f"期号{current_period}: 数据未正确加载，跳过")
                    current_period = tester.get_next_period(current_period)
                    continue
                
                # 创建号码选择器
                from number_selector import NumberSelector
                selector = NumberSelector(small_db, large_db, tester.ssqhistory_all, tester.parameters)
                
                # 获取第1组和第4组复式红蓝球号码
                group1_red, group1_blue = selector.get_group1_numbers()
                group4_red, group4_blue = selector.get_group4_numbers_with_params(nr, nb)
                
                # 获取排序后的第2组和第3组号码
                group2_red_sorted, group2_blue_sorted = tester.get_group2_sorted_numbers(large_db, nr, nb)
                group3_red_sorted, group3_blue_sorted = tester.get_group3_sorted_numbers(large_db, nr, nb)
                
                # 分别计算每组的最大命中情况
                group1_max_hit, group1_winning_period, group1_max_hit_count = tester.calculate_hit_rate(
                    group1_red, group1_blue, answer_db)
                group1_winning_red, group1_winning_blue = tester.get_winning_numbers(group1_winning_period)
                
                group2_red_set = set(group2_red_sorted)
                group2_blue_set = set(group2_blue_sorted)
                group2_max_hit, group2_winning_period, group2_max_hit_count = tester.calculate_hit_rate(
                    group2_red_set, group2_blue_set, answer_db)
                group2_winning_red, group2_winning_blue = tester.get_winning_numbers(group2_winning_period)
                
                group3_red_set = set(group3_red_sorted)
                group3_blue_set = set(group3_blue_sorted)
                group3_max_hit, group3_winning_period, group3_max_hit_count = tester.calculate_hit_rate(
                    group3_red_set, group3_blue_set, answer_db)
                group3_winning_red, group3_winning_blue = tester.get_winning_numbers(group3_winning_period)
                
                group4_max_hit, group4_winning_period, group4_max_hit_count = tester.calculate_hit_rate(
                    group4_red, group4_blue, answer_db)
                group4_winning_red, group4_winning_blue = tester.get_winning_numbers(group4_winning_period)
                
                # 保存结果 - 每期对应4行数据
                period_results = []
                
                # 第1组
                group1_blue_str = ','.join([f'{x:02d}' for x in sorted(group1_blue)]) if len(group1_blue) > 0 and 0 not in group1_blue else ''
                period_results.append({
                    '期号': current_period,
                    '组别': '第1组',
                    '红球号码': ','.join([f'{x:02d}' for x in sorted(group1_red)]),
                    '蓝球号码': group1_blue_str,
                    '最大命中情况': group1_max_hit,
                    '最大命中情况的次数': group1_max_hit_count,
                    '中奖期号': group1_winning_period,
                    '中奖红球号码': group1_winning_red,
                    '中奖蓝球号码': group1_winning_blue
                })
                
                # 第2组
                period_results.append({
                    '期号': current_period,
                    '组别': '第2组',
                    '红球号码': ','.join([f'{x:02d}' for x in group2_red_sorted]),
                    '蓝球号码': ','.join([f'{x:02d}' for x in group2_blue_sorted]) if len(group2_blue_sorted) > 0 else '',
                    '最大命中情况': group2_max_hit,
                    '最大命中情况的次数': group2_max_hit_count,
                    '中奖期号': group2_winning_period,
                    '中奖红球号码': group2_winning_red,
                    '中奖蓝球号码': group2_winning_blue
                })
                
                # 第3组
                period_results.append({
                    '期号': current_period,
                    '组别': '第3组',
                    '红球号码': ','.join([f'{x:02d}' for x in group3_red_sorted]),
                    '蓝球号码': ','.join([f'{x:02d}' for x in group3_blue_sorted]) if len(group3_blue_sorted) > 0 else '',
                    '最大命中情况': group3_max_hit,
                    '最大命中情况的次数': group3_max_hit_count,
                    '中奖期号': group3_winning_period,
                    '中奖红球号码': group3_winning_red,
                    '中奖蓝球号码': group3_winning_blue
                })
                
                # 第4组
                group4_blue_str = '0' if len(group4_blue) == 0 else ','.join([f'{x:02d}' for x in sorted(group4_blue)])
                period_results.append({
                    '期号': current_period,
                    '组别': '第4组',
                    '红球号码': ','.join([f'{x:02d}' for x in sorted(group4_red)]),
                    '蓝球号码': group4_blue_str,
                    '最大命中情况': group4_max_hit,
                    '最大命中情况的次数': group4_max_hit_count,
                    '中奖期号': group4_winning_period,
                    '中奖红球号码': group4_winning_red,
                    '中奖蓝球号码': group4_winning_blue
                })
                
                results.extend(period_results)
                test_count += 1
                
                print(f"已完成期号{current_period}，第4组最大命中: {group4_max_hit}")
                
                # 获取下一期期号
                current_period = tester.get_next_period(current_period)
                
            except Exception as e:
                print(f"处理期号{current_period}时发生错误: {e}")
                current_period = tester.get_next_period(current_period)
                continue
        
        print(f"\n批量测试完成，共处理{test_count}期")
        
        # 4. 保存结果
        if len(results) > 0:
            print("步骤4: 保存测试结果...")
            filepath = tester.save_results(results, small_periods, large_periods, nr, nb)
            print(f"结果已保存到: {filepath}")
        else:
            print("没有结果可保存")
        
    except Exception as e:
        print(f"程序执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_full_test()
