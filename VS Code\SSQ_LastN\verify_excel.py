"""
验证Excel文件中的排序是否正确
"""

import pandas as pd

def verify_excel():
    print("=== 验证Excel文件中的排序 ===")
    
    try:
        # 读取Excel文件
        filepath = "Output/Results_TestMain_SSQ_6_40_20_6_20250826.xlsx"
        df = pd.read_excel(filepath, sheet_name='详细结果')
        
        print(f"成功读取Excel文件: {filepath}")
        print(f"数据行数: {len(df)}")
        print(f"列名: {df.columns.tolist()}")
        
        # 查看第一期的4组数据
        first_period = df['期号'].iloc[0]
        first_period_data = df[df['期号'] == first_period]
        
        print(f"\n第一期（期号{first_period}）的4组数据:")
        for _, row in first_period_data.iterrows():
            print(f"{row['组别']}: 红球={row['红球号码']}, 蓝球={row['蓝球号码']}, 最大命中={row['最大命中情况']}")
        
        # 验证第2组和第3组是否不同
        group2_data = first_period_data[first_period_data['组别'] == '第2组']
        group3_data = first_period_data[first_period_data['组别'] == '第3组']
        
        if len(group2_data) > 0 and len(group3_data) > 0:
            group2_red = group2_data.iloc[0]['红球号码']
            group2_blue = group2_data.iloc[0]['蓝球号码']
            group3_red = group3_data.iloc[0]['红球号码']
            group3_blue = group3_data.iloc[0]['蓝球号码']
            
            if group2_red == group3_red and group2_blue == group3_blue:
                print("\n❌ 第2组和第3组完全相同！")
            else:
                print("\n✅ 第2组和第3组不同")
                print(f"第2组红球: {group2_red}")
                print(f"第3组红球: {group3_red}")
                print(f"第2组蓝球: {group2_blue}")
                print(f"第3组蓝球: {group3_blue}")
        
        # 统计各组的命中情况
        print("\n各组命中情况统计:")
        for group in ['第1组', '第2组', '第3组', '第4组']:
            group_data = df[df['组别'] == group]
            if len(group_data) > 0:
                avg_hit = group_data['最大命中情况'].mean()
                max_hit = group_data['最大命中情况'].max()
                min_hit = group_data['最大命中情况'].min()
                print(f"{group}: 平均命中={avg_hit:.1f}, 最大命中={max_hit}, 最小命中={min_hit}")
        
        # 查看第4组蓝球为0的情况
        group4_data = df[df['组别'] == '第4组']
        zero_blue_count = len(group4_data[group4_data['蓝球号码'] == '0'])
        print(f"\n第4组蓝球为0的期数: {zero_blue_count}/{len(group4_data)}")
        
        print("\n✅ Excel文件验证完成！")
        
    except Exception as e:
        print(f"验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_excel()
