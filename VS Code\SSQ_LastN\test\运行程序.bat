@echo off
chcp 65001 >nul
echo ========================================
echo SSQ选号程序
echo ========================================
echo.
echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

echo 正在检查依赖包...
python -c "import pandas, openpyxl, numpy" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误：依赖包安装失败
        pause
        exit /b 1
    )
)

echo 正在启动程序...
echo.
python main.py

echo.
echo 程序执行完毕
pause
