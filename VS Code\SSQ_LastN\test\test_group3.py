"""
测试第3组马尔科夫迁移概率计算
"""

from TestSSQ_Main import SSQBatchTester

def test_group3():
    print("=== 测试第3组马尔科夫迁移概率计算 ===")
    
    # 创建测试器
    tester = SSQBatchTester()
    
    # 加载数据
    print("加载数据...")
    tester.load_data()
    tester.load_parameters()
    
    # 获取测试数据
    large_db = tester.get_database_by_period_range(23001, 40, "before")
    print(f"大数据库期数: {len(large_db)}")
    
    # 测试第2组（历史概率）
    print("\n测试第2组（历史概率）...")
    group2_red, group2_blue = tester.get_group2_sorted_numbers(large_db, 20, 6)
    print(f"第2组红球: {group2_red}")
    print(f"第2组蓝球: {group2_blue}")
    
    # 测试第3组（马尔科夫迁移概率）
    print("\n测试第3组（马尔科夫迁移概率）...")
    group3_red, group3_blue = tester.get_group3_sorted_numbers(large_db, 20, 6)
    print(f"第3组红球: {group3_red}")
    print(f"第3组蓝球: {group3_blue}")
    
    # 比较两组是否不同
    if group2_red == group3_red and group2_blue == group3_blue:
        print("\n❌ 第2组和第3组完全相同！")
        return False
    else:
        print("\n✅ 第2组和第3组不同，马尔科夫计算成功！")
        return True

if __name__ == "__main__":
    test_group3()
