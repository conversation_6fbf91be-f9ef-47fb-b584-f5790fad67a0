"""
SSQ选号程序测试脚本
用于验证程序各个模块的功能
"""

import sys
import traceback
from data_loader import SSQDataLoader
from ssq_analyzer import SSQAnalyzer
from combination_generator import CombinationGenerator
from result_saver import ResultSaver


def test_data_loader():
    """测试数据加载模块"""
    print("=== 测试数据加载模块 ===")
    try:
        loader = SSQDataLoader()
        data = loader.load_data()
        
        print(f"✓ 数据加载成功，共{len(data)}期")
        
        # 测试获取最新期信息
        period, red_balls, blue_ball = loader.get_latest_period_info()
        print(f"✓ 最新一期: {period}期，红球: {red_balls}，蓝球: {blue_ball}")
        
        # 测试获取当前数据库
        current_db = loader.get_current_database(3)
        print(f"✓ 获取最新3期数据成功，期号范围: {current_db['期号'].min()}-{current_db['期号'].max()}")
        
        return True, loader
    except Exception as e:
        print(f"✗ 数据加载模块测试失败: {e}")
        return False, None


def test_ssq_analyzer():
    """测试SSQ分析模块"""
    print("\n=== 测试SSQ分析模块 ===")
    try:
        # 测试各种判断函数
        assert SSQAnalyzer.is_big_ball(17, False) == True  # 红球17是大球
        assert SSQAnalyzer.is_big_ball(16, False) == False  # 红球16是小球
        assert SSQAnalyzer.is_big_ball(9, True) == True   # 蓝球9是大球
        assert SSQAnalyzer.is_big_ball(8, True) == False  # 蓝球8是小球
        print("✓ 大小球判断功能正常")
        
        assert SSQAnalyzer.is_odd_ball(5) == True   # 5是奇数
        assert SSQAnalyzer.is_odd_ball(6) == False  # 6是偶数
        print("✓ 奇偶球判断功能正常")
        
        assert SSQAnalyzer.is_prime_ball(1) == True   # 1是质数（按定义）
        assert SSQAnalyzer.is_prime_ball(2) == True   # 2是质数
        assert SSQAnalyzer.is_prime_ball(4) == False  # 4不是质数
        assert SSQAnalyzer.is_prime_ball(7) == True   # 7是质数
        print("✓ 质数球判断功能正常")
        
        # 测试和值计算
        red_balls = [1, 2, 3, 4, 5, 6]
        assert SSQAnalyzer.calculate_sum(red_balls) == 21
        print("✓ 和值计算功能正常")
        
        # 测试AC值计算
        red_balls = [6, 10, 17, 19, 25, 31]
        ac_value = SSQAnalyzer.calculate_ac_value(red_balls)
        print(f"✓ AC值计算功能正常，测试组合AC值: {ac_value}")
        
        return True
    except Exception as e:
        print(f"✗ SSQ分析模块测试失败: {e}")
        return False


def test_combination_generator(loader):
    """测试排列组合生成模块"""
    print("\n=== 测试排列组合生成模块 ===")
    try:
        # 使用较小的数据集进行测试
        current_db = loader.get_current_database(3)
        generator = CombinationGenerator(current_db)
        
        red_count, blue_count, total_combinations = generator.get_unique_balls_info()
        print(f"✓ 基本信息获取成功: 红球{red_count}个，蓝球{blue_count}个，总组合{total_combinations}个")
        
        # 测试概率计算
        red_prob_df, blue_prob_df = generator.calculate_historical_probabilities()
        print(f"✓ 概率计算成功: 红球概率表{len(red_prob_df)}行，蓝球概率表{len(blue_prob_df)}行")
        
        # 验证概率和为1
        red_prob_sum = red_prob_df['历史出现概率'].sum()
        blue_prob_sum = blue_prob_df['历史出现概率'].sum()
        print(f"✓ 概率验证: 红球概率和={red_prob_sum:.6f}，蓝球概率和={blue_prob_sum:.6f}")
        
        return True, generator
    except Exception as e:
        print(f"✗ 排列组合生成模块测试失败: {e}")
        return False, None


def test_result_saver():
    """测试结果保存模块"""
    print("\n=== 测试结果保存模块 ===")
    try:
        saver = ResultSaver()
        
        # 测试文件名生成
        filename = saver.generate_filename(25096, 5)
        print(f"✓ 文件名生成成功: {filename}")
        
        return True, saver
    except Exception as e:
        print(f"✗ 结果保存模块测试失败: {e}")
        return False, None


def run_small_test():
    """运行小规模完整测试"""
    print("\n=== 运行小规模完整测试 ===")
    try:
        # 加载数据
        loader = SSQDataLoader()
        data = loader.load_data()
        
        # 获取最新2期数据进行测试
        current_db = loader.get_current_database(2)
        print(f"使用最新2期数据进行测试")
        
        # 生成组合
        generator = CombinationGenerator(current_db)
        red_count, blue_count, total_combinations = generator.get_unique_balls_info()
        print(f"将生成 {total_combinations} 个组合")
        
        if total_combinations > 1000:
            print("组合数量较大，跳过完整分析测试")
            return True
        
        # 分析所有组合
        results_df = generator.analyze_all_combinations()
        print(f"✓ 完整分析成功，生成{len(results_df)}行结果")
        
        # 计算概率
        red_prob_df, blue_prob_df = generator.calculate_historical_probabilities()
        
        # 保存结果
        saver = ResultSaver()
        latest_period, _, _ = loader.get_latest_period_info()
        filepath = saver.save_results(results_df, red_prob_df, blue_prob_df, latest_period, 2)
        print(f"✓ 结果保存成功: {filepath}")
        
        return True
    except Exception as e:
        print(f"✗ 小规模完整测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("SSQ选号程序功能测试")
    print("=" * 50)
    
    all_passed = True
    
    # 测试各个模块
    success, loader = test_data_loader()
    all_passed &= success
    
    success = test_ssq_analyzer()
    all_passed &= success
    
    if loader:
        success, generator = test_combination_generator(loader)
        all_passed &= success
    
    success, saver = test_result_saver()
    all_passed &= success
    
    # 运行小规模完整测试
    if loader:
        success = run_small_test()
        all_passed &= success
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✓ 所有测试通过！程序功能正常。")
    else:
        print("✗ 部分测试失败，请检查程序。")
    
    return all_passed


if __name__ == "__main__":
    main()
