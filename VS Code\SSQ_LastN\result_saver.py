"""
结果保存模块
负责将分析结果保存到Excel文件
"""

import pandas as pd
import os
from datetime import datetime
from typing import Tuple


class ResultSaver:
    """结果保存器"""
    
    def __init__(self, output_dir: str = "Output"):
        """
        初始化结果保存器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def generate_filename(self, latest_period: int, small_range: int, large_range: int, nr: int, nb: int) -> str:
        """
        生成Excel文件名

        Args:
            latest_period: 最新一期期号
            small_range: 小数据库的数据范围
            large_range: 大数据库的数据范围
            nr: 红球数量
            nb: 蓝球数量

        Returns:
            文件名
        """
        # 获取当前日期
        current_date = datetime.now().strftime("%Y%m%d")

        # 按照命名规则：Result_AAAA_BBBB_CCCC_DDDD_EEEE_FFFF_GGGG
        filename = f"Result_SSQ_{latest_period}_{small_range}_{large_range}_{nr}_{nb}_{current_date}.xlsx"

        return filename
    
    def save_results(self,
                    results_df: pd.DataFrame,
                    red_prob_df: pd.DataFrame,
                    blue_prob_df: pd.DataFrame,
                    red_transition_df: pd.DataFrame,
                    blue_transition_df: pd.DataFrame,
                    red_markov_df: pd.DataFrame,
                    blue_markov_df: pd.DataFrame,
                    latest_period: int,
                    small_range: int,
                    large_range: int,
                    nr: int,
                    nb: int,
                    group1_red: set = None,
                    group1_blue: set = None,
                    group2_red: set = None,
                    group2_blue: set = None,
                    group3_red: set = None,
                    group3_blue: set = None,
                    group4_red: set = None,
                    group4_blue: set = None,
                    group2_red_sorted: list = None,
                    group2_blue_sorted: list = None,
                    group3_red_sorted: list = None,
                    group3_blue_sorted: list = None) -> str:
        """
        保存分析结果到Excel文件

        Args:
            results_df: 详细分析结果
            red_prob_df: 红球概率表
            blue_prob_df: 蓝球概率表
            red_transition_df: 红球历史跟随性概率表
            blue_transition_df: 蓝球历史跟随性概率表
            red_markov_df: 红球马尔科夫概率表
            blue_markov_df: 蓝球马尔科夫概率表
            latest_period: 最新一期期号
            small_range: 小数据库范围
            large_range: 大数据库范围
            nr: 红球数量
            nb: 蓝球数量

        Returns:
            保存的文件路径
        """
        # 生成文件名
        filename = self.generate_filename(latest_period, small_range, large_range, nr, nb)
        filepath = os.path.join(self.output_dir, filename)



        # 创建Excel写入器
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            # 保存详细结果到"详细结果"标签页
            results_df.to_excel(writer, sheet_name='详细结果', index=False)

            # 保存红球历史出现概率到"红球历史出现概率"标签页
            red_prob_df.to_excel(writer, sheet_name='红球历史出现概率', index=False)

            # 保存蓝球历史出现概率到"蓝球历史出现概率"标签页
            blue_prob_df.to_excel(writer, sheet_name='蓝球历史出现概率', index=False)

            # 保存红球号码历史跟随性概率到"红球号码历史跟随性概率"标签页
            red_transition_df.to_excel(writer, sheet_name='红球号码历史跟随性概率', index=False)

            # 保存蓝球号码历史跟随性概率到"蓝球号码历史跟随性概率"标签页
            blue_transition_df.to_excel(writer, sheet_name='蓝球号码历史跟随性概率', index=False)

            # 保存红球号码迁移概率到"红球号码迁移概率"标签页
            red_markov_df.to_excel(writer, sheet_name='红球号码迁移概率', index=False)

            # 保存蓝球号码迁移概率到"蓝球号码迁移概率"标签页
            blue_markov_df.to_excel(writer, sheet_name='蓝球号码迁移概率', index=False)

            # 创建汇总信息标签页
            summary_data = {
                '项目': [
                    '最新一期期号',
                    '小数据库范围（期数）',
                    '大数据库范围（期数）',
                    '红球数量NR',
                    '蓝球数量NB',
                    '筛选后组合数量',
                    '生成时间'
                ],
                '值': [
                    latest_period,
                    small_range,
                    large_range,
                    nr,
                    nb,
                    len(results_df),
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                ]
            }
            summary_df = pd.DataFrame(summary_data)

            # 创建4组复式红蓝球号码信息
            group_data = []
            if group1_red is not None and group1_blue is not None:
                group_data.append({
                    '组别': '第1组复式红蓝球号码',
                    '红球号码': ' '.join([f"{num:02d}" for num in sorted(group1_red)]),
                    '蓝球号码': ' '.join([f"{num:02d}" for num in sorted(group1_blue)])
                })
            if group2_red is not None and group2_blue is not None:
                # 使用排序后的数据，如果没有则使用原始数据
                red_display = group2_red_sorted if group2_red_sorted is not None else sorted(group2_red)
                blue_display = group2_blue_sorted if group2_blue_sorted is not None else sorted(group2_blue)
                group_data.append({
                    '组别': '第2组复式红蓝球号码',
                    '红球号码': ' '.join([f"{num:02d}" for num in red_display]),
                    '蓝球号码': ' '.join([f"{num:02d}" for num in blue_display])
                })
            if group3_red is not None and group3_blue is not None:
                # 使用排序后的数据，如果没有则使用原始数据
                red_display = group3_red_sorted if group3_red_sorted is not None else sorted(group3_red)
                blue_display = group3_blue_sorted if group3_blue_sorted is not None else sorted(group3_blue)
                group_data.append({
                    '组别': '第3组复式红蓝球号码',
                    '红球号码': ' '.join([f"{num:02d}" for num in red_display]),
                    '蓝球号码': ' '.join([f"{num:02d}" for num in blue_display])
                })
            if group4_red is not None and group4_blue is not None:
                group_data.append({
                    '组别': '第4组复式红蓝球号码',
                    '红球号码': ' '.join([f"{num:02d}" for num in sorted(group4_red)]),
                    '蓝球号码': ' '.join([f"{num:02d}" for num in sorted(group4_blue)])
                })

            # 保存汇总信息到第一个工作表
            summary_df.to_excel(writer, sheet_name='汇总信息', index=False)

            # 如果有组别数据，保存到同一个工作表的下方
            if group_data:
                group_df = pd.DataFrame(group_data)
                # 获取当前工作表
                workbook = writer.book
                worksheet = writer.sheets['汇总信息']

                # 在汇总信息下方添加空行和组别信息
                start_row = len(summary_df) + 3  # 汇总信息后留2行空白
                group_df.to_excel(writer, sheet_name='汇总信息', startrow=start_row, index=False)


        return filepath
    
    def print_sample_results(self, results_df: pd.DataFrame, sample_size: int = 10):
        """
        打印部分结果样例

        Args:
            results_df: 结果DataFrame
            sample_size: 样例数量
        """


        # 选择要显示的列（新格式只有红蓝球号码和遗漏值）
        display_columns = [
            '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球'
        ]

        sample_df = results_df.head(sample_size)

        for idx, row in sample_df.iterrows():
            red_balls = f"{int(row['红球1']):02d} {int(row['红球2']):02d} {int(row['红球3']):02d} {int(row['红球4']):02d} {int(row['红球5']):02d} {int(row['红球6']):02d}"
            blue_ball = f"{int(row['蓝球']):02d}"

            # 显示遗漏值
            red_omissions = f"{int(row['红球1遗漏'])} {int(row['红球2遗漏'])} {int(row['红球3遗漏'])} {int(row['红球4遗漏'])} {int(row['红球5遗漏'])} {int(row['红球6遗漏'])}"
            blue_omission = f"{int(row['蓝球遗漏'])}"




    
    def print_probability_summary(self, red_prob_df: pd.DataFrame, blue_prob_df: pd.DataFrame):
        """
        打印概率统计摘要
        
        Args:
            red_prob_df: 红球概率表
            blue_prob_df: 蓝球概率表
        """

