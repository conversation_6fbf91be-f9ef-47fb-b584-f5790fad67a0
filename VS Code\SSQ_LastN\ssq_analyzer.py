"""
SSQ分析模块
负责分析SSQ号码的各种特性
"""

import pandas as pd
from typing import List, Tu<PERSON>, Dict
from itertools import combinations


class SSQAnalyzer:
    """SSQ号码分析器"""
    
    @staticmethod
    def is_big_ball(number: int, is_blue: bool = False) -> bool:
        """
        判断是否为大球号码
        
        Args:
            number: 号码
            is_blue: 是否为蓝球
            
        Returns:
            是否为大球号码
        """
        if is_blue:
            return number > 8  # 蓝球大于8为大球
        else:
            return number > 16  # 红球大于16为大球
    
    @staticmethod
    def is_odd_ball(number: int) -> bool:
        """
        判断是否为奇球号码
        
        Args:
            number: 号码
            
        Returns:
            是否为奇球号码
        """
        return number % 2 == 1
    
    @staticmethod
    def is_prime_ball(number: int) -> bool:
        """
        判断是否为质球号码（包括1）
        
        Args:
            number: 号码
            
        Returns:
            是否为质球号码
        """
        if number == 1:
            return True
        if number < 2:
            return False
        if number == 2:
            return True
        if number % 2 == 0:
            return False
        
        for i in range(3, int(number**0.5) + 1, 2):
            if number % i == 0:
                return False
        return True
    
    @staticmethod
    def calculate_sum(red_balls: List[int]) -> int:
        """
        计算红球号码和值
        
        Args:
            red_balls: 红球号码列表
            
        Returns:
            和值
        """
        return sum(red_balls)
    
    @staticmethod
    def calculate_ac_value(red_balls: List[int]) -> int:
        """
        计算AC值（号码数字复杂度）
        
        Args:
            red_balls: 红球号码列表（已排序）
            
        Returns:
            AC值
        """
        if len(red_balls) < 2:
            return 0
        
        # 计算任意两个号码之间不相同的正差值
        differences = set()
        for i in range(len(red_balls)):
            for j in range(i + 1, len(red_balls)):
                diff = abs(red_balls[i] - red_balls[j])
                differences.add(diff)
        
        # AC值 = 不相同的正差值总个数 - (正选号码数量-1)
        ac_value = len(differences) - (len(red_balls) - 1)
        return ac_value
    
    @staticmethod
    def calculate_omission(current_db: pd.DataFrame, red_balls: List[int], blue_ball: int) -> List[int]:
        """
        计算遗漏值
        
        Args:
            current_db: 当前数据库
            red_balls: 红球号码列表
            blue_ball: 蓝球号码
            
        Returns:
            遗漏值列表（6个红球 + 1个蓝球）
        """
        omissions = []
        
        # 计算红球遗漏值
        for red_ball in red_balls:
            omission = SSQAnalyzer._calculate_single_omission(current_db, red_ball, is_blue=False)
            omissions.append(omission)
        
        # 计算蓝球遗漏值
        if blue_ball == 0:
            blue_omission = -1  # 蓝球为0时，遗漏值设为-1
        else:
            blue_omission = SSQAnalyzer._calculate_single_omission(current_db, blue_ball, is_blue=True)
        omissions.append(blue_omission)
        
        return omissions
    
    @staticmethod
    def _calculate_single_omission(current_db: pd.DataFrame, ball_number: int, is_blue: bool = False) -> int:
        """
        计算单个号码的遗漏值
        
        Args:
            current_db: 当前数据库
            ball_number: 球号码
            is_blue: 是否为蓝球
            
        Returns:
            遗漏值
        """
        # 按期号倒序查找
        db_reversed = current_db.sort_values('期号', ascending=False)
        
        if is_blue:
            # 查找蓝球
            for idx, row in db_reversed.iterrows():
                if row['蓝球'] == ball_number:
                    # 找到最近一次出现的位置
                    latest_period_idx = db_reversed.index[0]  # 最新一期的索引
                    current_period_idx = idx  # 当前找到的期的索引
                    
                    # 计算遗漏值（期数差）
                    omission = latest_period_idx - current_period_idx
                    return omission
        else:
            # 查找红球
            red_columns = ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']
            for idx, row in db_reversed.iterrows():
                if ball_number in [row[col] for col in red_columns]:
                    # 找到最近一次出现的位置
                    latest_period_idx = db_reversed.index[0]  # 最新一期的索引
                    current_period_idx = idx  # 当前找到的期的索引
                    
                    # 计算遗漏值（期数差）
                    omission = latest_period_idx - current_period_idx
                    return omission
        
        # 如果在当前数据库中没有找到，返回数据库长度作为遗漏值
        return len(current_db)
    
    @staticmethod
    def analyze_combination(red_balls: List[int], blue_ball: int, current_db: pd.DataFrame) -> Dict:
        """
        分析一组红蓝球号码的所有特性
        
        Args:
            red_balls: 红球号码列表
            blue_ball: 蓝球号码
            current_db: 当前数据库
            
        Returns:
            包含所有分析结果的字典
        """
        # 确保红球按从小到大排序
        sorted_red_balls = sorted(red_balls)
        
        # 分析大小特性
        red_big_count = sum(1 for ball in sorted_red_balls if SSQAnalyzer.is_big_ball(ball, False))
        blue_big_count = 1 if SSQAnalyzer.is_big_ball(blue_ball, True) else 0
        
        # 分析奇偶特性
        red_odd_count = sum(1 for ball in sorted_red_balls if SSQAnalyzer.is_odd_ball(ball))
        blue_odd_count = 1 if SSQAnalyzer.is_odd_ball(blue_ball) else 0
        
        # 分析质合特性
        red_prime_count = sum(1 for ball in sorted_red_balls if SSQAnalyzer.is_prime_ball(ball))
        blue_prime_count = 1 if SSQAnalyzer.is_prime_ball(blue_ball) else 0
        
        # 计算和值
        red_sum = SSQAnalyzer.calculate_sum(sorted_red_balls)
        
        # 计算AC值
        ac_value = SSQAnalyzer.calculate_ac_value(sorted_red_balls)
        
        # 计算遗漏值
        omissions = SSQAnalyzer.calculate_omission(current_db, sorted_red_balls, blue_ball)
        
        return {
            'red_balls': sorted_red_balls,
            'blue_ball': blue_ball,
            'red_big_count': red_big_count,
            'blue_big_count': blue_big_count,
            'red_odd_count': red_odd_count,
            'blue_odd_count': blue_odd_count,
            'red_prime_count': red_prime_count,
            'blue_prime_count': blue_prime_count,
            'red_sum': red_sum,
            'ac_value': ac_value,
            'omissions': omissions  # [红球1遗漏, 红球2遗漏, ..., 红球6遗漏, 蓝球遗漏]
        }
