"""
测试第2组和第3组的排序功能
"""

import pandas as pd
from TestSSQ_Main import SSQBatchTester

def test_sorting():
    print("=== 测试第2组和第3组的排序功能 ===")
    
    try:
        # 创建测试器
        tester = SSQBatchTester()
        
        # 加载数据
        print("1. 加载数据...")
        tester.load_data()
        tester.load_parameters()
        print("   数据加载成功")
        
        # 获取测试数据
        print("2. 获取大数据库...")
        large_db = tester.get_database_by_period_range(23001, 40, "before")
        print(f"   大数据库期数: {len(large_db)}")
        
        # 测试第2组（历史概率排序）
        print("3. 测试第2组（历史概率排序）...")
        group2_red, group2_blue = tester.get_group2_sorted_numbers(large_db, 10, 5)
        print(f"   第2组红球: {group2_red}")
        print(f"   第2组蓝球: {group2_blue}")
        
        # 测试第3组（马尔科夫迁移概率排序）
        print("4. 测试第3组（马尔科夫迁移概率排序）...")
        group3_red, group3_blue = tester.get_group3_sorted_numbers(large_db, 10, 5)
        print(f"   第3组红球: {group3_red}")
        print(f"   第3组蓝球: {group3_blue}")
        
        # 验证排序规则
        print("5. 验证排序规则...")
        
        # 验证第2组红球是否按概率排序（概率相等时按号码排序）
        print("   验证第2组红球排序...")
        red_counts = {}
        for _, row in large_db.iterrows():
            for col in ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']:
                ball = row[col]
                red_counts[ball] = red_counts.get(ball, 0) + 1
        
        total_red_count = sum(red_counts.values())
        red_probs = [(ball, count / total_red_count) for ball, count in red_counts.items()]
        sorted_red_probs = sorted(red_probs, key=lambda x: (-x[1], x[0]))
        
        expected_red = [ball for ball, prob in sorted_red_probs[:10]]
        if group2_red == expected_red:
            print("   ✅ 第2组红球排序正确")
        else:
            print("   ❌ 第2组红球排序错误")
            print(f"   期望: {expected_red}")
            print(f"   实际: {group2_red}")
        
        # 比较两组是否不同
        print("6. 比较两组差异...")
        if group2_red == group3_red and group2_blue == group3_blue:
            print("   ❌ 第2组和第3组完全相同！")
            return False
        else:
            print("   ✅ 第2组和第3组不同")
            
            # 显示差异
            red_diff = set(group2_red) - set(group3_red)
            blue_diff = set(group2_blue) - set(group3_blue)
            
            if red_diff:
                print(f"   红球差异: 第2组独有 {red_diff}")
            if blue_diff:
                print(f"   蓝球差异: 第2组独有 {blue_diff}")
            
            return True
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_sorting()
    if success:
        print("\n🎉 排序功能测试通过！")
    else:
        print("\n❌ 排序功能测试失败！")
