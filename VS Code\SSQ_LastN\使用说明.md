# SSQ选号程序使用说明

## 程序概述

本程序是基于SSQ（双色球）历史数据进行选号分析的Python程序，采用初选和筛选的两阶段策略，能够根据用户指定的两个期数范围（小数据库和大数据库），生成符合特定条件的红蓝球组合。

## 主要功能

1. **数据读取**: 从Excel文件中读取SSQ历史数据
2. **双数据库策略**: 使用小数据库和大数据库进行不同的分析
3. **初选号码**: 通过去重和概率分析获得候选号码
4. **筛选组合**: 根据大小、奇偶、质合、和值、AC值等条件筛选
5. **遗漏分析**: 计算筛选后号码的遗漏值
6. **概率统计**: 基于大数据库计算红蓝球号码的历史出现概率
7. **结果导出**: 将分析结果保存为Excel文件

## 文件结构

```
SSQ_LastN/
├── main.py                    # 主程序入口
├── data_loader.py            # 数据读取模块
├── ssq_analyzer.py           # SSQ分析模块
├── combination_generator.py  # 排列组合生成模块
├── result_saver.py           # 结果保存模块
├── test_program.py           # 测试脚本
├── requirements.txt          # 依赖包列表
├── lottery_data_all.xlsx     # SSQ历史数据文件
├── Output/                   # 输出目录
├── README.md                 # 英文说明文档
└── 使用说明.md               # 中文使用说明
```

## 安装和运行

### 1. 环境要求
- Python 3.7 或更高版本
- 必要的Python包：pandas, openpyxl, numpy

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 运行程序
```bash
python main.py
```

### 4. 运行测试
```bash
python test_program.py
```

## 使用步骤

1. **启动程序**: 运行 `python main.py`
2. **输入小数据库期数**: 根据提示输入小数据库的期数（正整数）
3. **输入大数据库期数**: 根据提示输入大数据库的期数（正整数，应≥小数据库期数）
4. **输入选号参数**: 输入想要选择的红球数量和蓝球数量（用逗号隔开，如：20,6）
5. **等待初选**: 程序会自动进行号码初选
6. **检查交集**: 如果交集为空，程序会提示重新输入参数
7. **等待筛选**: 程序会根据设定条件筛选符合要求的组合
8. **查看结果**: 分析完成后，结果会保存在Output目录中

## 程序逻辑

### 初选阶段
1. **第1组号码**: 从小数据库中提取所有出现过的红蓝球号码（去重）
2. **第2组号码**: 从大数据库中选择概率最高的NR个红球和NB个蓝球
3. **第3组号码**: 取第1组和第2组的交集作为最终候选号码
4. **交集检查**: 如果交集中红球或蓝球数量为0，提示重新输入NR和NB

### 筛选阶段
对第3组号码进行6红球+1蓝球的组合，并按以下条件筛选：
1. 与原始数据库中每1组红蓝球号码不完全一致（7个号码不全相等）
2. 红球大球号码3个或4个
3. 红球奇球号码3个或4个
4. 红球质球号码2个或3个
5. 红球和值在80-119之间
6. 红球AC值等于7或8

## 术语说明

### 基本概念
- **期号**: 每期的编号，格式为年份+序号（如25096表示2025年第96期）
- **红球**: SSQ中的前6个球，号码范围1-33
- **蓝球**: SSQ中的第7个球，号码范围1-16

### 特性定义
- **大球号码**: 红球>16，蓝球>8
- **奇球号码**: 号码为奇数
- **质球号码**: 号码为质数（包括1）
- **和值**: 6个红球号码之和
- **AC值**: 号码数字复杂度，计算公式为：任意两个号码间不同正差值个数 - (号码数量-1)
- **遗漏值**: 号码自上次开出到本期的间隔期数

## 输出文件说明

程序会在Output目录中生成Excel文件，文件名格式：
`Result_SSQ_{最新期号}_{小数据库期数}_{大数据库期数}_{日期}.xlsx`

### Excel文件包含3个标签页：

#### 1. 详细结果
包含筛选后组合的分析数据：
- 红球1-6：排序后的红球号码
- 蓝球：蓝球号码
- 红球1-6遗漏：各红球的遗漏值（基于小数据库计算）
- 蓝球遗漏：蓝球的遗漏值（基于小数据库计算）

#### 2. 概率结果
基于大数据库的历史出现概率统计：
- 红球号码：红球号码（1-33）
- 红球历史出现概率：在大数据库期数内的出现概率
- 蓝球号码：蓝球号码（1-16）
- 蓝球历史出现概率：在大数据库期数内的出现概率

#### 3. 汇总信息
程序运行的基本信息：
- 最新一期期号
- 小数据库范围（期数）
- 大数据库范围（期数）
- 筛选后组合数量
- 生成时间

## 性能说明

### 组合数量计算
需要检查的组合数 = C(第3组红球数量, 6) × 第3组蓝球数量
实际筛选出的组合数取决于筛选条件的严格程度

### 性能建议
- **小规模测试**: 小数据库5-10期，大数据库20-30期
- **中等规模**: 小数据库10-20期，大数据库50-100期
- **大规模分析**: 小数据库20期以上，大数据库100期以上

### 时间估算
- 小数据库5期，大数据库20期：约1-3分钟
- 小数据库10期，大数据库50期：约5-15分钟
- 小数据库20期，大数据库100期：约30分钟-2小时

### 筛选效果
- 筛选条件较严格，通常只有少部分组合能通过筛选
- 如果没有符合条件的组合，建议增加数据库期数或调整筛选条件

## 注意事项

1. **数据文件**: 确保`lottery_data_all.xlsx`文件存在且格式正确
2. **内存使用**: 大规模分析时注意内存使用情况
3. **磁盘空间**: 确保有足够空间存储结果文件
4. **程序中断**: 可以随时按Ctrl+C中断程序
5. **结果验证**: 建议先用小规模数据测试验证结果

## 常见问题

### Q: 程序运行很慢怎么办？
A: 减少分析的期数，或者升级硬件配置。

### Q: 内存不足怎么办？
A: 减少分析的期数，或者关闭其他程序释放内存。

### Q: 结果文件很大怎么办？
A: 这是正常现象，可以使用Excel的筛选功能查看特定条件的结果。

### Q: 如何选择合适的期数？
A: 建议根据分析目的选择：
- 短期趋势分析：5-20期
- 中期模式分析：20-50期
- 长期统计分析：50期以上

## 技术支持

如有问题，请检查：
1. Python版本是否符合要求
2. 依赖包是否正确安装
3. 数据文件是否存在且完整
4. 运行测试脚本是否通过
