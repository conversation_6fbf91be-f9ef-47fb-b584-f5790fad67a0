"""
马尔科夫链分析模块
负责计算历史跟随性概率和号码迁移概率
"""

import pandas as pd
import numpy as np
from typing import Tuple, List, Dict


class MarkovAnalyzer:
    """马尔科夫链分析器"""
    
    def __init__(self, current_db: pd.DataFrame):
        """
        初始化马尔科夫链分析器
        
        Args:
            current_db: 当前数据库
        """
        self.current_db = current_db
    
    def calculate_red_ball_transition_matrix(self) -> np.ndarray:
        """
        计算红球号码历史跟随性概率矩阵（33x33）
        矩阵定义：列代表前1期号码，行代表后1期号码
        每列的概率和为1

        Returns:
            33x33的转移概率矩阵
        """
        # 初始化33x33的计数矩阵
        # transition_counts[i, j] 表示前1期出现红球j+1，后1期出现红球i+1的次数
        transition_counts = np.zeros((33, 33))

        # 获取红球列
        red_columns = ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']

        # 遍历相邻两期
        for i in range(len(self.current_db) - 1):
            # 获取第i期的红球号码（前1期）
            current_period_reds = set([self.current_db.iloc[i][col] for col in red_columns])
            # 获取第i+1期的红球号码（后1期）
            next_period_reds = set([self.current_db.iloc[i+1][col] for col in red_columns])

            # 统计跟随关系
            for red1 in current_period_reds:  # 前1期出现的红球
                for red2 in next_period_reds:  # 后1期出现的红球
                    # transition_counts[red2-1, red1-1] += 1
                    # 行索引：后1期红球号码-1，列索引：前1期红球号码-1
                    transition_counts[red2-1, red1-1] += 1

        # 转换为概率矩阵（每列的概率和为1）
        transition_matrix = np.zeros((33, 33))
        for j in range(33):  # 遍历每一列（前1期号码）
            col_sum = np.sum(transition_counts[:, j])
            if col_sum > 0:
                transition_matrix[:, j] = transition_counts[:, j] / col_sum
            else:
                # 如果某个红球从未在前1期出现，设置均匀分布
                transition_matrix[:, j] = 1.0 / 33

        return transition_matrix
    
    def calculate_blue_ball_transition_matrix(self) -> np.ndarray:
        """
        计算蓝球号码历史跟随性概率矩阵（16x16）
        矩阵定义：列代表前1期号码，行代表后1期号码
        每列的概率和为1

        Returns:
            16x16的转移概率矩阵
        """
        # 初始化16x16的计数矩阵
        # transition_counts[i, j] 表示前1期出现蓝球j+1，后1期出现蓝球i+1的次数
        transition_counts = np.zeros((16, 16))

        # 遍历相邻两期
        for i in range(len(self.current_db) - 1):
            # 获取第i期的蓝球号码（前1期）
            current_blue = self.current_db.iloc[i]['蓝球']
            # 获取第i+1期的蓝球号码（后1期）
            next_blue = self.current_db.iloc[i+1]['蓝球']

            # 统计跟随关系
            # transition_counts[next_blue-1, current_blue-1] += 1
            # 行索引：后1期蓝球号码-1，列索引：前1期蓝球号码-1
            transition_counts[next_blue-1, current_blue-1] += 1

        # 转换为概率矩阵（每列的概率和为1）
        transition_matrix = np.zeros((16, 16))
        for j in range(16):  # 遍历每一列（前1期号码）
            col_sum = np.sum(transition_counts[:, j])
            if col_sum > 0:
                transition_matrix[:, j] = transition_counts[:, j] / col_sum
            else:
                # 如果某个蓝球从未在前1期出现，设置均匀分布
                transition_matrix[:, j] = 1.0 / 16

        return transition_matrix
    
    def calculate_markov_probabilities(self, latest_red_balls: List[int], latest_blue_ball: int,
                                     red_transition_matrix: np.ndarray, blue_transition_matrix: np.ndarray,
                                     red_historical_probs: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        基于马尔科夫链计算号码迁移概率
        
        Args:
            latest_red_balls: 最新一期的红球号码列表
            latest_blue_ball: 最新一期的蓝球号码
            red_transition_matrix: 红球转移概率矩阵
            blue_transition_matrix: 蓝球转移概率矩阵
            red_historical_probs: 红球历史出现概率DataFrame
            
        Returns:
            (红球马尔科夫概率表, 蓝球马尔科夫概率表)
        """
        # 计算红球马尔科夫概率
        # 构建SA矩阵：33行6列，包含最新一期6个红球对应的列向量
        # 由于矩阵定义：列代表前1期号码，行代表后1期号码
        # 所以red_transition_matrix[:, red_ball-1]表示前1期出现red_ball时，后1期各号码的概率
        SA = np.zeros((33, 6))
        for i, red_ball in enumerate(latest_red_balls):
            SA[:, i] = red_transition_matrix[:, red_ball-1]
        
        # 构建SB向量：6行1列，包含最新一期6个红球的历史出现概率
        SB = np.zeros((6, 1))
        for i, red_ball in enumerate(latest_red_balls):
            # 从历史概率表中查找对应概率
            prob_row = red_historical_probs[red_historical_probs['红球号码'] == red_ball]
            if not prob_row.empty:
                SB[i, 0] = prob_row.iloc[0]['历史出现概率']
            else:
                SB[i, 0] = 1.0 / 33  # 默认概率
        
        # 计算红球马尔科夫链向量：SA × SB
        red_markov_vector = np.dot(SA, SB).flatten()
        
        # 归一化
        red_markov_vector = red_markov_vector / np.sum(red_markov_vector)
        
        # 生成红球马尔科夫概率表
        red_markov_df = pd.DataFrame({
            '红球号码': list(range(1, 34)),
            '号码迁移概率': red_markov_vector
        })
        
        # 计算蓝球马尔科夫概率
        # 直接使用最新一期蓝球对应的列向量
        # 由于矩阵定义：列代表前1期号码，行代表后1期号码
        # 所以blue_transition_matrix[:, latest_blue_ball-1]表示前1期出现latest_blue_ball时，后1期各号码的概率
        blue_markov_vector = blue_transition_matrix[:, latest_blue_ball-1]

        # 归一化蓝球概率
        if np.sum(blue_markov_vector) > 0:
            blue_markov_vector = blue_markov_vector / np.sum(blue_markov_vector)
        
        # 生成蓝球马尔科夫概率表
        blue_markov_df = pd.DataFrame({
            '蓝球号码': list(range(1, 17)),
            '号码迁移概率': blue_markov_vector
        })
        
        return red_markov_df, blue_markov_df
    
    def get_top_markov_numbers(self, red_markov_df: pd.DataFrame, blue_markov_df: pd.DataFrame,
                              nr: int, nb: int) -> Tuple[set, set]:
        """
        获取马尔科夫概率最高的红蓝球号码
        
        Args:
            red_markov_df: 红球马尔科夫概率表
            blue_markov_df: 蓝球马尔科夫概率表
            nr: 红球数量
            nb: 蓝球数量
            
        Returns:
            (红球号码集合, 蓝球号码集合)
        """
        # 按概率从大到小排序，如果概率相等则按号码从小到大排序
        red_sorted = red_markov_df.sort_values(['号码迁移概率', '红球号码'], ascending=[False, True])
        top_red_balls = red_sorted.head(nr)['红球号码'].tolist()

        # 按概率从大到小排序，如果概率相等则按号码从小到大排序
        blue_sorted = blue_markov_df.sort_values(['号码迁移概率', '蓝球号码'], ascending=[False, True])
        top_blue_balls = blue_sorted.head(nb)['蓝球号码'].tolist()
        
        return set(top_red_balls), set(top_blue_balls)

    def get_top_markov_numbers_sorted(self, red_markov_df: pd.DataFrame, blue_markov_df: pd.DataFrame,
                                     nr: int, nb: int) -> Tuple[list, list]:
        """
        获取马尔科夫概率最高的红蓝球号码 - 返回排序后的列表

        Args:
            red_markov_df: 红球马尔科夫概率表
            blue_markov_df: 蓝球马尔科夫概率表
            nr: 红球数量
            nb: 蓝球数量

        Returns:
            (红球号码列表, 蓝球号码列表) - 按概率从大到小排序，概率相等时按号码从小到大排序
        """
        # 按概率从大到小排序，如果概率相等则按号码从小到大排序
        red_sorted = red_markov_df.sort_values(['号码迁移概率', '红球号码'], ascending=[False, True])
        top_red_balls = red_sorted.head(nr)['红球号码'].tolist()

        # 按概率从大到小排序，如果概率相等则按号码从小到大排序
        blue_sorted = blue_markov_df.sort_values(['号码迁移概率', '蓝球号码'], ascending=[False, True])
        top_blue_balls = blue_sorted.head(nb)['蓝球号码'].tolist()

        return top_red_balls, top_blue_balls
    
    def get_probability_for_numbers(self, numbers: List[int], prob_df: pd.DataFrame, 
                                   number_col: str, prob_col: str) -> List[float]:
        """
        获取指定号码的概率值
        
        Args:
            numbers: 号码列表
            prob_df: 概率DataFrame
            number_col: 号码列名
            prob_col: 概率列名
            
        Returns:
            概率值列表
        """
        probabilities = []
        for number in numbers:
            prob_row = prob_df[prob_df[number_col] == number]
            if not prob_row.empty:
                probabilities.append(prob_row.iloc[0][prob_col])
            else:
                # 如果找不到，使用默认概率
                if '红球' in number_col:
                    probabilities.append(1.0 / 33)
                else:
                    probabilities.append(1.0 / 16)
        return probabilities

    def get_red_ball_transition_table(self) -> pd.DataFrame:
        """
        获取红球号码历史跟随性概率表
        矩阵定义：列代表前1期号码，行代表后1期号码
        表格格式：每行代表后1期号码，每列代表前1期号码的跟随概率

        Returns:
            红球历史跟随性概率表
        """
        # 计算转移矩阵
        transition_matrix = self.calculate_red_ball_transition_matrix()

        # 创建列名：第一列是后1期红球号码，其余列是前1期各红球号码的跟随概率
        columns = ['后1期红球号码'] + [f'前1期红球{i}' for i in range(1, 34)]

        # 构建数据
        data = []
        for i in range(33):  # 遍历后1期红球号码
            # 第一列：后1期红球号码
            # 其余列：前1期各红球号码跟随到当前红球的概率
            row = [i + 1] + transition_matrix[i, :].tolist()
            data.append(row)

        # 创建DataFrame
        df = pd.DataFrame(data, columns=columns)
        return df

    def get_blue_ball_transition_table(self) -> pd.DataFrame:
        """
        获取蓝球号码历史跟随性概率表
        矩阵定义：列代表前1期号码，行代表后1期号码
        表格格式：每行代表后1期号码，每列代表前1期号码的跟随概率

        Returns:
            蓝球历史跟随性概率表
        """
        # 计算转移矩阵
        transition_matrix = self.calculate_blue_ball_transition_matrix()

        # 创建列名：第一列是后1期蓝球号码，其余列是前1期各蓝球号码的跟随概率
        columns = ['后1期蓝球号码'] + [f'前1期蓝球{i}' for i in range(1, 17)]

        # 构建数据
        data = []
        for i in range(16):  # 遍历后1期蓝球号码
            # 第一列：后1期蓝球号码
            # 其余列：前1期各蓝球号码跟随到当前蓝球的概率
            row = [i + 1] + transition_matrix[i, :].tolist()
            data.append(row)

        # 创建DataFrame
        df = pd.DataFrame(data, columns=columns)
        return df
